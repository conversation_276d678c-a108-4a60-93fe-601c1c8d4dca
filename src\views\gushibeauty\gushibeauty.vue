<template>
  <div class="localcuisine">
    <Header/>
    <div class="banner">
      <img src="../../assets/01.jpg" alt=""/>
    </div>
    <div class="item">
      <div class="item-box">
        <div class="item-l">
          <div class="mlgs">
            <div></div>
            <div class="title">走进固始</div>
            <div></div>
          </div>
          <ul>
            <li @click="getMeishi(8)" :class="id == 8 ? 'active_list' : ''">固始美食</li>
            <li @click="getMeishi(3)" :class="id == 3 ? 'active_list' : ''">旅游景点</li>
            <li @click="getMeishi(21)" :class="id == 21 ? 'active_list' : ''">健康生活</li>
            <li @click="getMeishi(39)" :class="id == 39 ? 'active_list' : ''">非遗文化</li>
            <li @click="getMeishi(7)" :class="id == 7 ? 'active_list' : ''">本地商超</li>
            <li @click="getMeishi(10)" :class="id == 10 ? 'active_list' : ''">城市风光</li>
            <li @click="getMeishi(11)" :class="id == 11 ? 'active_list' : ''">图说固始</li>
            <li @click="getMeishi(45)" :class="id == 45 ? 'active_list' : ''">招商引资</li>
            <li @click="getMeishi(40)" :class="id == 40 ? 'active_list' : ''">名人轶事</li>
            <li @click="getMeishi(4)" :class="id == 4 ? 'active_list' : ''">招聘</li>
            <li @click="getMeishi(38)" :class="id == 38 ? 'active_list' : ''">养老</li>
            <li @click="getMeishi(35)" :class="id == 35 ? 'active_list' : ''">资讯</li>
          </ul>
        </div>
        <div class="item-r">
          <el-breadcrumb class="crumbs" separator-class="el-icon-arrow-right">
            <el-breadcrumb-item
            >
              <router-link
                style="color: #606266; font-weight: initial"
                :to="{ path: '/' }"
              >首页
              </router-link
              >
            </el-breadcrumb-item>
            <el-breadcrumb-item>{{ name }}</el-breadcrumb-item>
          </el-breadcrumb>
          <div class="lists">
            <div
              class="finefood"
              v-for="item in scenicList.slice(
                (currentPage - 1) * pageSize,
                currentPage * pageSize
              )"
              :key="item.id"
              @click="goDetail(item.id)"
            >
              <img v-if="item.news_image[0]" :src="item.news_image[0]" alt=""/>
              <div class="finefood-item">
                <div class="title">{{ item.news_title }}</div>
                <div class="desc">
                  {{ item.news_titleshort }}
                </div>
                <div class="viewdetails" style="display: block">
                  <div class="btn">查看详情</div>
                  <!-- <div class="tour">VR游览</div> -->
                </div>
              </div>
            </div>
          </div>
          <div class="fenye">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="total"
              :current-page="currentPage"
              :page-size="pageSize"
              @current-change="current_change"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- <recommend /> -->
    <Footers/>
  </div>
</template>
<script>
import Header from '@/components/header/header.vue'
import Footers from '@/components/footers/footers.vue'
import newsImgList from '@/components/newsImgList/newsImgList.vue'
import recommend from '@/components/recommend/recommend.vue'
import { currentPolitics } from '@/api/api.js'

export default {
  name: 'gushibeauty',
  data () {
    return {
      scenicList: [],
      total: 1,
      currentPage: 1,
      page: 1,
      pageSize: 6,
      loading: false,
      id: 0,
      name: '',
    }
  },
  mounted () {
    var id = 0
    if (typeof (this.$route.params.id) == 'undefined') {
      id = 8
    } else {
      id = this.$route.params.id
    }
    console.log(id);
    this.getMeishi(id)
    this.getInfoName(parseInt(id));
  },
  computed: {},
  methods: {
    getInfoName (id) {
      switch (id) {
        case  8:
          this.name = '固始美食'
          break
        case  3:
          this.name = '旅游景点'
          break
        case  21:
          this.name = '健康生活'
          break
        case  39:
          this.name = '非遗文化'
          break
        case  7:
          this.name = '本地商超'
          break
        case  10:
          this.name = '城市风光'
          break
        case  11:
          this.name = '图说固始'
          break
        case  45:
          this.name = '招商引资'
          break
        case  4:
          this.name = '招聘'
          break
        case  40:
          this.name = '名人轶事'
          break
        case  38:
          this.name = '养老'
          break
        case  35:
          this.name = '资讯'
          break
      }
    },
    getMeishi (type) {
      this.getInfoName(type);
      this.loading = true
      //3
      this.id = type
      currentPolitics(type).then((res) => {
        console.log(res)
        this.scenicList = res.data.data.news
        this.total = this.scenicList.length
        this.currentPage = 1
        this.loading = false
      })
    },
    current_change (currentPage) {
      //改变当前页
      this.currentPage = currentPage
      window.scrollTo(0, 0)
    },
    goDetail (id) {
      var key = this.id
      if (key == 3) {
        this.$router.push({
          path: `/scenicdetails/${id}`,
        })
      } else if (key == 8) {
        this.$router.push({
          path: `/gourmetdetails/${id}`,
        })
      } else {
        this.$router.push({
          path: `/newsdetail/${id}`,
        })
      }
    },
  },
  components: {
    Header,
    Footers,
    newsImgList,
    recommend,
  },
}
</script>
<style scoped lang="scss">
.active_list {
  background: linear-gradient(0deg, #bf0614, #ff4c4c) !important;
  color: #ffffff !important;
}

.banner {
  // height: 360px;
  width: 100%;

  img {
    // height: 360px;
    width: 100%;
    object-fit: cover;
    display: block;
    min-height: 140px;
  }
}

.item {
  .item-box {
    max-width: 1420px;
    margin: 0 auto;
    display: flex;
  }

  .item-l {
    width: 330px;
    transform: translateY(-80px);
    margin-right: 30px;

    .mlgs {
      width: 330px;
      height: 240px;
      background: linear-gradient(0deg, #bf0614, #ff4c4c);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30px 0;
      justify-content: space-between;

      div {
        font-size: 32px;
        font-weight: 400;
        color: #ffffff;

        &:nth-child(1) {
          width: 120px;
          height: 40px;
          border: 1px solid #ffffff;
          border-bottom: none;
        }

        &:nth-child(3) {
          width: 120px;
          height: 40px;
          border: 1px solid #ffffff;
          border-top: none;
        }
      }
    }

    ul {
      li {
        width: 330px;
        height: 80px;
        font-size: 24px;
        font-weight: 400;
        color: #333333;
        line-height: 80px;
        text-align: center;

        a {
          font-size: 24px;
          font-weight: 400;
          color: #333333;
        }

        &:nth-child(odd) {
          background: #ffffff;
        }

        &:nth-child(even) {
          background: #f7f7f7;
        }

        &:hover {
          background: linear-gradient(0deg, #bf0614, #ff4c4c);
          color: #ffffff;
        }

        &:hover a {
          color: #ffffff;
        }

        cursor: pointer;
      }
    }
  }

  .item-r {
    width: 1060px;

    .crumbs {
      font-size: 16px;
      font-weight: 400;
      color: #808080;
      height: 50px;
      background: #f7f7f7;
      line-height: 50px;
      padding-left: 16px;
      border-bottom: 1px solid #cccccc;
    }

    .lists {
      .finefood {
        display: flex;
        border-bottom: 1px dashed #808080;
        padding: 30px 0;

        &:hover .finefood-item .viewdetails .btn {
          background: #bf0614;
        }

        cursor: pointer;

        img {
          width: 340px;
          height: 204px;
          border-radius: 8px;
          margin-right: 30px;
        }

        .finefood-item {
          height: 204px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .title {
            font-size: 24px;
            font-weight: 400;
            color: #333333;
          }

          .desc {
            font-size: 20px;
            font-weight: 400;
            color: #808080;
            line-height: 40px;
            margin-top: 10px;
            overflow: hidden; //超出的文本隐藏
            display: -webkit-box;
            -webkit-line-clamp: 3; // 超出多少行
            -webkit-box-orient: vertical;
          }

          .viewdetails {
            display: flex;
            justify-content: flex-end;

            .btn {
              width: 120px;
              height: 40px;
              background: #cccccc;
              border-radius: 20px;
              font-size: 20px;
              font-weight: 400;
              color: #ffffff;
              line-height: 40px;
              text-align: center;
              margin-right: 20px;
              cursor: pointer;
            }

            .tour {
              width: 120px;
              height: 40px;
              background: linear-gradient(-90deg, #46a1bc, #46cc9a);
              border-radius: 20px;
              font-size: 20px;
              font-weight: 400;
              color: #ffffff;
              line-height: 40px;
              text-align: center;
              cursor: pointer;
            }
          }
        }
      }
    }

    .fenye {
      margin: 41px 0 60px;
      text-align: center;
    }
  }
}

.jctj {
  background: #f7f7f7;
  padding-top: 57px;
  padding-bottom: 132px;

  .jctj-item {
    width: 1420px;
    margin: 0 auto;

    .jctj-img {
      height: 63px;
      display: block;
      margin: 0 auto 59px;
    }

    .culture {
      display: flex;
      justify-content: space-between;
    }
  }
}

@media screen and (max-width: 1120px) {
  .item .item-l {
    display: none;
  }
  .item .item-r {
    margin: 0 auto;
  }
}

@media screen and (max-width: 750px) {
  // .item .item-r .lists .finefood {
  //   padding: 30px 30px;
  //   display: block;
  // }
  // .item .item-r .lists .finefood .finefood-item {
  //   margin-top: 20px;
  // }
}

@media screen and (max-width: 750px) {
  .item .item-r .fenye {
    margin: 20px 0 25px;
  }
  .item .item-l {
    display: none;
  }
  .item .item-r {
    margin: 0 auto;
  }
  .item .item-r .lists .finefood {
    padding: 15px 10px;
    display: flex;
  }
  .item .item-r .lists .finefood img {
    width: 138px;
    height: 92px;
    margin-right: 10px;
  }
  .item .item-r .lists .finefood .finefood-item .viewdetails {
    display: none;
  }
  .item .item-r .lists .finefood .finefood-item {
    margin-top: 0;
  }
  .item .item-r .lists .finefood .finefood-item .title {
    font-size: 20px;
    width: 100%;
    overflow: hidden; //超出的文本隐藏
    display: -webkit-box;
    -webkit-line-clamp: 1; // 超出多少行
    -webkit-box-orient: vertical;
  }
  .item .item-r .lists .finefood .finefood-item .desc {
    font-size: 16px;
    line-height: 1.5;
    -webkit-line-clamp: 2;
  }
  .item .item-r .lists .finefood .finefood-item {
    height: auto;
  }
}
</style>
<style>
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background: linear-gradient(0deg, #bf0614, #ff4c4c);
}
</style>
