<template>
  <div>
    <Header />
    <div class="banner">
      <img src="../../assets/01.jpg" alt="" />
    </div>
    <div class="crumbs-item">
      <el-breadcrumb class="crumbs" separator-class="el-icon-arrow-right">
        <el-breadcrumb-item
          ><router-link
            style="color: #606266; font-weight: initial"
            :to="{ path: '/' }"
            >首页</router-link
          >
        </el-breadcrumb-item>
        <el-breadcrumb-item>搜索</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="main_item">
      <div class="input-item">
        <el-input placeholder="请输入内容" v-model="input3">
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="getSearch()"
            @keydown.enter.native="enter"
          ></el-button>
        </el-input>
      </div>
      <div class="lists_box">
        <template v-if="newsList" v-for="item in newsList">
          <div
            class="lists"
            @click="goDetail(item.id, item.gory_id)"  :key="item.id"
          >
            <img :src="item.news_image[0]" alt="" v-if="item.news_image" />
            <div
              class="lists_right"
              :class="
                item.news_image.length < 1 ? 'lists_right1' : 'lists_right'
              "
            >
              <div class="data">{{ item.showtime }}</div>
              <div class="lists_title">{{ item.news_title }}</div>
              <el-button class="btn" size="mini" type="info" round
                >查看详情</el-button
              >
            </div>
          </div>
          <div style="background-color: #DFE1E5;height: 1px;width: 100%;"></div>
        </template>
        <template v-if="newsList.length == 0">
          <el-empty :image-size="200"></el-empty>
        </template>
        <div class="fenye">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            @current-change="current_change"
            @prev-click="prev_click"
            @next-click="next_click"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- <recommend /> -->
    <Footers />
  </div>
</template>
<script>
import Header from "@/components/header/header.vue";
import Footers from "@/components/footers/footers.vue";
import recommend from "@/components/recommend/recommend.vue";
import { search } from "@/api/api.js";
export default {
  data() {
    return {
      page: 1,
      pageSize: 10,
      newsList: [],
      total: 1,
      currentPage: 1,
      lists: [],
      i: 0,
      id: 0,
      num: 0,
      pid: 0,
      input3: "",
    };
  },
  mounted(d) {
    var key=this.$route.query.key;
    this.input3=key;
    this.getSearch();
  },
  watch: {
    $route: {
      // 监听路由的参数变化
      handler() {
        this.getSearch();
      },
      immediate: true, // 立即执行
    },
  },
  created() {
    document.addEventListener("keydown", (e) => {
      let key = window.event.keyCode;
      if (key == 13) {
        // 13是enter键的键盘码 如果等于13 就调用click的登录方法
        this.getSearch();
      }
    });
  },
  beforeDestroy() {
    //这里的 this.login('loginForm') 指的是键盘事件的方法名
    document.removeEventListener("keydown", this.getSearch());
  },

  computed: {},
  methods: {
    getSearch(page) {
      if (this.input3 !== "") {
        search(this.input3, page).then((res) => {
          console.log(res);
          this.newsList = res.data.data.list;
          this.total = res.data.data.count;
          this.currentPage = 1;
        });
      }
    },
    prev_click() {
      this.page--;
      this.getSearch(this.page);
    },
    next_click() {
      this.page++;
      this.getSearch(this.page);
    },
    goDetail(id, gory_id) {
      // console.log(id);
      if (gory_id == 8) {
        this.$router.push({
          path: `/gourmetdetails/${id}`,
        });
      } else if (gory_id == 3) {
        this.$router.push({
          path: `/scenicdetails/${id}`,
        });
      } else {
        this.$router.push({
          path: `/newsdetail/${id}`,
        });
      }
    },
    current_change(currentPage) {
      //改变当前页
      this.currentPage = currentPage;
      this.getSearch(currentPage);
      // window.scrollTo(0, 0);
    },
  },
  components: {
    Header,
    Footers,
    recommend,
  },
};
</script>
<style scoped lang="scss">
.banner {
  // height: 360px;
  width: 100%;
  img {
    // height: 360px;
    width: 100%;
    min-height: 140px;
    object-fit: cover;
    display: block;
  }
}
.crumbs-item {
  background: #f7f7f7;
  border-bottom: 1px solid #cccccc;
  .crumbs {
    height: 50px;
    max-width: 1420px;
    margin: 0 auto;
    line-height: 50px;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
  }
}
.main_item {
  background: #f7f7f7;
  padding: 30px 0 60px;
  .main_item-active {
    background: linear-gradient(0deg, #ff8603, #ff6301);
    color: #fff;
  }
  .input-item {
    max-width: 500px;
    margin: 0 auto 30px;
  }
  ul {
    display: flex;
    justify-content: flex-start;
    max-width: 1420px;
    margin: 0 auto 40px;
    flex-wrap: wrap;
    li {
      padding: 0 25px;
      background: #e6e6e6;
      border-radius: 100px;
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      margin: 0 20px;
      box-sizing: border-box;
      height: 40px;
      line-height: 40px;
      margin-top: 10px;
      cursor: pointer;
    }
  }
  .lists_box {
    max-width: 1420px;
    margin: 0 auto;
    @keyframes myfirst {
      0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
      }
      100% {
        opacity: 1;
        -webkit-transform: translateX(0);
      }
    }
    /*使用动画：将动画绑定元素上*/
    .lists {
      animation: myfirst 1s;
      -moz-animation: myfirst 1s; /* Firefox */
      -webkit-animation: myfirst 1s; /* Safari 和 Chrome */
      -o-animation: myfirst 1s; /* Opera */
    }

    .lists {
      padding: 20px 30px;
      display: flex;
      align-items: center;
      background: #ffffff;
      //box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
      //border-radius: 8px;
      //margin-bottom: 30px;
      transition: all 0.3s;
      //border-bottom: 1px solid #999999;
      cursor: pointer;

      &:hover {
        transform: scale(1.005);
        box-shadow: 0 5px 30px rgb(0 0 0 / 10%);
      }
      &:hover .lists_right .btn {
        background: linear-gradient(0deg, #bf0614, #ff4c4c);
        color: #fff;
      }
      img {
        max-height: 200px;
        max-width: 200px;
        height: max-content;
        margin-right: 20px;
        border-radius: 6px;
      }
      .lists_right {
        .btn {
          background: #e6e6e6;
          color: #808080;
          border: none;
          // width: 120px;
          // height: 40px;
        }
        .data {
          font-size: 16px;
          font-weight: 400;
          color: #808080;
        }
        .lists_title {
          font-size: 20px;
          font-weight: 400;
          color: #333333;
          margin: 15px 0 15px;
        }
        .data1 {
          margin: 0 0 20px;
          font-size: 20px;
          font-weight: 400;
          color: #808080;
        }
      }
      .lists_right1 {
        .data {
          font-size: 16px;
          font-weight: 400;
          color: #808080;
          margin: 0 0 10px;
        }
        .lists_title {
          font-size: 20px;
          font-weight: 400;
          color: #333333;
          margin-bottom: 15px;
        }
      }
    }
    .fenye {
      display: flex;
      justify-content: center;
    }
  }
}
@media screen and (max-width: 1480px) {
  .crumbs-item .crumbs {
    padding: 0 30px;
  }
  .main_item {
    padding: 30px 30px 60px;
  }
}
@media screen and (max-width: 1120px) {
  .crumbs-item .crumbs {
    padding: 0 30px;
  }
  .main_item {
    padding: 30px 30px 60px;
  }
}
@media screen and (max-width: 450px) {
  .main_item .lists_box .lists {
    display: block;
  }
}
</style>
