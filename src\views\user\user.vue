<template>
  <div>
    <Header />
    <div class="content">
      <div class="item">
        <div class="title">
          <div>留言</div>
          <!-- <div>没有账号，<span @click="goRegister()">立即注册</span></div> -->
        </div>
        <div class="login">
          <div class="l">
            <!-- <p>Welcome</p>
            <p>固始电视台</p> -->
            <img src="../../assets/welcone.png" alt="" />
          </div>
          <div class="r">
            <!-- <el-input
              prefix-icon="el-icon-phone"
              class="inp"
              v-model="phone"
              placeholder="请输入手机号"
            >
            </el-input>
            <el-input
              prefix-icon="el-icon-s-custom"
              v-model="password"
              placeholder="请输入密码"
            ></el-input>
            <div class="forget">
              <el-button class="btn" type="primary">登录</el-button>
              <router-link :to="{ name: 'password' }"
                ><div>忘记密码</div></router-link
              > 
            </div>-->
            <el-input
              type="textarea"
              :rows="4"
              placeholder="请输入留言内容"
              v-model="textarea"
            >
            </el-input>
            <div>
              <el-button class="btn" type="primary" @click="message()"
                >留言</el-button
              >
              <span @click="logout()">退出登录</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Footers />
  </div>
</template>
<script>
import Header from "@/components/header/header.vue";
import Footers from "@/components/footers/footers.vue";
import { content } from "@/api/api.js";
export default {
  data() {
    return {
      phone: "",
      password: "",
      textarea: "",
    };
  },
  mounted() {},
  computed: {},
  methods: {
    goRegister() {
      this.$router.push({ name: "register" });
    },
    logout() {
      this.$store.commit("delToken");
      this.$router.push({ name: "index" });
    },
    message() {
      const username = this.$store.state.username;
      const mobile = this.$store.state.mobile;
      const email = this.$store.state.email;
      const data = {
        username: username,
        mobile: mobile,
        email: email,
        content: this.textarea,
      };
      content(data).then((res) => {
        this.$message({
          message: "您的留言已成功提交，会有工作人员联系您",
          type: "success",
        });
        this.textarea = "";
      });
    },
  },
  components: {
    Header,
    Footers,
  },
};
</script>
<style scoped lang="scss">
.content {
  padding: 100px 0 100px;
  background: url("../../assets/loginbj.png");
  .item {
    max-width: 800px;
    margin: 0 auto;
    .title {
      width: 100%;
      font-size: 20px;
      font-weight: bold;
      color: #0f4b9d;
      background-color: #fff;
      padding: 20px 40px;
      display: flex;
      justify-content: space-between;
      div {
        &:nth-child(2) {
          font-size: 12px;
          color: #808080;
        }
        span {
          color: #0f4b9d;
          cursor: pointer;
        }
      }
    }
    .login {
      display: flex;
      padding: 20px 40px;
      background-color: #fff;
      justify-content: center;
      .l {
        width: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 80%;
        }
      }
      .r {
        width: 50%;
        .inp {
          margin: 20px 0;
        }
        .btn {
          width: 70%;
          margin: 20px 0 60px;
        }
        .forget {
          display: flex;
          justify-content: space-between;
          div {
            font-size: 12px;
            color: #808080;
            font-weight: bold;
            margin-top: 42px;
            cursor: pointer;
          }
        }
        span {
          display: inline-block;
          font-size: 12px;
          color: #808080;
          font-weight: bold;
          margin-top: 42px;
          cursor: pointer;
          vertical-align: bottom;
          margin: 0 0 60px 30px;
        }
      }
    }
  }
}
@media screen and (max-width: 750px) {
  .content {
    padding: 100px 30px 100px;
  }
  .content .item .login .l {
    display: none;
  }
}
@media screen and (max-width: 450px) {
  // .content .item .login .r .forget {
  //   display: block;
  // }
  // .content .item .login .r .forget div {
  //   margin-top: -45px;
  // }
  .content .item .login {
    padding: 20px 0px;
  }
  .content .item .login .r {
    width: 85%;
  }
  .content .item .login .r .btn {
    margin: 20px 0 10px;
  }
  .content .item .login .r span {
    margin: 0 0 10px 0px;
  }
  .content .item .login .r div {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }
}
</style>
