<template>
  <div class="index" v-cloak>

    <nav>

      <div class="nav-box">
        <div class="tou">
          <div class="logo">
            <img src="../../assets/logo.png" alt="" />
          </div>
          <!-- <div style="cursor: pointer;" @click="openUrl(1)">
            <img src="../../assets/top1.jpg" alt="" style="height: 70px" />
          </div> -->
          <div v-if="webIsPc == 1"
            style="display: flex; align-self: center; align-items: center;justify-content: space-between;">
            <div
              style="width: 400px;display: flex; align-self: center; align-items: center;justify-content: start;margin-right: 25px;box-sizing: border-box;border-radius: 6px;position: relative;background-color: white;opacity: 0.7;">
              <input
                style="border-left: 1px solid #ccc;border-top: 1px solid #ccc;border-bottom: 1px solid #ccc;border-right: 0px;display: block;width: 100%;box-sizing: border-box;background-color: transparent;padding: 8px 12px;line-height: 24px; height: 40px;font-size: 16px;outline: none;"
                v-model="serCounte" placeholder="请输入关键字"></input>
              <el-button style="border-radius:0px;" slot="append" icon="el-icon-search" @click="goSearch()"
                v-model="serCounte"></el-button>
            </div>
            <div style="display: flex;align-items: center;font-size: 16px;font-weight: 400;color: gray;">
              <div style="padding: 4px 12px;border: 1px solid gray;border-radius: 100px;margin-right: 10px;">网站支持IPv6
              </div>
              <!--              <div class="wza" style="cursor: pointer;">无障碍</div>-->
              <!--              &nbsp;|&nbsp;-->
              <!--              <div style="display: flex;align-items: center;" v-if="registerText == true">-->
              <!--                <div class="wza" @click="goLogin()" style="cursor: pointer;">登录</div>-->
              <!--                &nbsp;|&nbsp;-->
              <!--                <div class="wza" @click="goZhuce()" style="cursor: pointer;">注册</div>-->
              <!--              </div>-->
              <div class="liuyan" v-if="registerText == false" @click="goLiuyan()">
                留言
              </div>
            </div>
          </div>
          <div v-if="webIsPc == 2" class="self" style="position: fixed;right: 10px;top: 25px;font-size: 30px">
            <div v-if="!topBan" class="el-icon-more" @click="dianji()" style="color: #999999"></div>
            <!-- <div class="el-icon-more" @click="drawer = true"></div> -->
            <div v-if="topBan" class="el-icon-close" @click="dianji()" style="color: #999999"></div>
          </div>
        </div>

      </div>
    </nav>
    <nav v-if="webIsPc == 1" style="background-color: #bf0614;height: auto;">
      <div class="nav-item" style="justify-content: center;color: #FFFFFF">
        <div class="nav-right">
          <div class="nav-title">
            <ul>
              <li>
                <router-link :to="{ path: '/' }">
                  首页
                </router-link>
              </li>
              <li>
                <router-link :to="{ path: '/charmgushi' }">
                  走进固始
                </router-link>
              </li>
              <li>
                <router-link :to="{ path: `/news/${countSzyw.id}/${14}` }">时政要闻
                </router-link>
              </li>
              <li>
                <router-link :to="{ path: `/newsinfo/${15}` }">政策信息
                </router-link>
              </li>
              <li>
                <router-link :to="{ path: `/news/${countZtzl.id}/${13}` }">专题专栏
                </router-link>
              </li>
              <li>
                <router-link :to="{ path: `/newsinfo/${9}` }">公示公告
                </router-link>
              </li>
              <li>
                <router-link :to="{ path: `/gushiys/${6}` }">固始营商
                </router-link>
              </li>
            </ul>

          </div>
        </div>
      </div>
    </nav>
    <nav v-if="webIsPc == 2">
      <div v-if="topBan" class="nav-item" style="justify-content: center;color: #FFFFFF">
        <div class="nav-right1">
          <!-- <div class="ipv6">网站支持IPv6</div> -->

          <div class="nav-title">
            <ul>
              <li>
                <router-link :to="{ path: `/charmgushi` }">
                  走进固始
                </router-link>
              </li>
              <li class="szyw-sz" id="szyw_sz1">
                <div class="nav-title-icon">
                  <router-link :to="{ path: `/news/${countSzyw.id}/${14}` }">时政要闻
                  </router-link>
                  <div :class="flag1 == false
                    ? ' el-icon-arrow-right sz'
                    : 'el-icon-arrow-down'
                    " @click.stop="szyw_sz()"></div>
                </div>
                <ul class="two-item two-item1">
                  <li v-for="item in szywTwoList" :key="item.id" @click="goNewsList(item.id, item.pid)">
                    {{ item.name }}
                  </li>
                </ul>
              </li>
              <li>
                <router-link :to="{ path: `/newsinfo/${15}` }">政策信息
                </router-link>
              </li>
              <li>
                <router-link :to="{ path: `/news/${countZtzl.id}/${13}` }">专题专栏
                </router-link>
              </li>
              <li>
                <router-link :to="{ path: `/newsinfo/${9}` }">公示公告
                </router-link>
              </li>
              <li class="szyw-sz" id="szyw_sz2">
                <div class="nav-title-icon">
                  <router-link :to="{ path: `/gushiys/${6}` }">固始营商
                  </router-link>
                  <div :class="flag == false
                    ? ' el-icon-arrow-right ys'
                    : 'el-icon-arrow-down'
                    " @click.stop="ysgs_ys()"></div>
                </div>
                <ul class="two-item two-item2">
                  <li v-for="item in gsysTwoList" :key="item.id" @click="goNewsList(item.id, item.pid)">
                    {{ item.name }}
                  </li>
                </ul>
              </li>
            </ul>
            <!-- <div class="search">
              <input
                type="text"
                id="search-inp2"
                placeholder="搜一下~"
                @focus="focuWidth()"
                autocomplete="off"
              />
              <img src="../../assets/搜索.png" alt="" @click="goSearch2()" />
            </div> -->
          </div>
          <div class="wza">无障碍</div>
          <div class="login" v-if="registerText == true">
            <div class="login-item" @click="goLogin()">登录</div>
            <div class="zhuce" @click="goZhuce()">注册</div>
          </div>
          <div class="liuyan" v-if="registerText == false" @click="goLiuyan()">
            留言
          </div>
        </div>
      </div>
    </nav>
    <div class="content">
      <!-- <div class="cbl">
        <div class="cbl-b" @click="open_web(1)">
          <img src="../../assets/形状554.png" alt="" />
          <div class="cbl-name">招聘信息</div>
        </div>
        <div class="cbl-b" @click="open_web(2)">
          <img src="../../assets/形状5541.png" alt="" />
          <div class="cbl-name">考试公告</div>
        </div>
      </div> -->
      <div class="content-item">
        <!--        <div>-->
        <!--          <img class="bj" :src="beijing_image1" alt="" @click="go_blank1()"/>-->
        <!--        </div>-->
        <div class="news">
          <template v-if="indexList.length > 0">
            <div class="news-title" @click="goDetail(indexList[0].id)">
              {{ indexList[0].news_title }}
            </div>
            <div class="news-list">
              <span v-if="indexList[1]" class="news-list-name" @click="goDetail(indexList[1].id)">{{
                indexList[1].news_title
              }}</span>&nbsp;&nbsp;<span class="gang">|</span>&nbsp;&nbsp;<span v-if="indexList[2]"
                class="news-list-name" @click="goDetail(indexList[2].id)">{{ indexList[2].news_title
                }}</span>&nbsp;&nbsp;<span class="gang">|</span>&nbsp;&nbsp;
              <span v-if="indexList[3]" class="news-list-name" @click="goDetail(indexList[3].id)">{{
                indexList[3].news_title
              }}</span>
            </div>
          </template>
        </div>
        <div class="banner-box">
          <div class="banner">
            <!--            <div class="bj-box"></div>-->
            <div class="banner-item">
              <!--              <div class="up-left"></div>-->
              <div class="banner-item-news">
                <!--                <div v-if="hotList" class="news-title" @click="goDetail(hotList.id)">-->
                <!--                  {{ hotList.news_title }}-->
                <!--                </div>-->
                <ul>
                  <li v-for="item in recommemdList" :key="item.id" @click="goDetail(item.id)">
                    <span>【{{ item.name }}】</span>
                    <span v-if="item.news_title">{{ item.news_title }}</span>
                  </li>
                </ul>
              </div>
              <div class="swiper-container swiper1">
                <div class="swiper-wrapper">
                  <div class="swiper-slide" v-for="item in newList" :key="item.id" :data-href="fun_str(item.id)">
                    <img :src="item.news_image[0]" alt="" />
                    <div v-if="item.news_title" class="swiper-title">
                      {{ item.news_title }}
                    </div>
                  </div>
                </div>
                <!-- 如果需要分页器 -->
                <div class="swiper-pagination"></div>
              </div>
              <div class="banner-item-news" id="banner-item-news">
                <div v-if="hotList" class="news-title" @click="goDetail(hotList.id)">
                  {{ hotList.news_title }}
                </div>
                <ul>
                  <li v-for="item in recommemdList" :key="item.id" @click="goDetail(item.id)">
                    <span>【{{ item.name }}】</span>
                    <span v-if="item.news_title">{{ item.news_title }}</span>
                  </li>
                </ul>
              </div>
            </div>
            <div class="banner-bottom">
              <div class="el-icon-arrow-left banner-icon banner-icon1"></div>
              <div class="el-icon-arrow-left yidong1"></div>
              <div class=".swiper-container swiper2">
                <div class="swiper-wrapper">
                  <div class="swiper-slide" v-for="item in featureList" :key="item.id" :data-href="fun_str(item)">
                    <img :src="item.image" alt="" />
                  </div>
                </div>
              </div>
              <div class=".swiper-container swiper5">
                <div class="swiper-wrapper">
                  <div class="swiper-slide" v-for="item in featureList" :key="item.id" :data-href="fun_str(item.id)">
                    <img :src="item.image" alt="" />
                  </div>
                </div>
              </div>
              <div class="el-icon-arrow-right banner-icon banner-icon2"></div>
              <div class="el-icon-arrow-right yidong2"></div>
            </div>
          </div>
        </div>

        <div class="szyw">
          <div class="szyw-img">
            <!-- <img class="szyw-img" src="../../assets/ywzx.png" alt="" /> -->
            <div class="bj">NEWS</div>
            <div class="txt">时政要闻</div>
          </div>
          <div class="szyw-item">
            <newsList :szywTitle="mediaGushiTitle" :newsList="mediaGushiList.slice(0, 8)" :id="27" :pid="14" />
            <newsList :szywTitle="gushiNewsTitle" :newsList="gushiNewsList.slice(0, 8)" :id="26" :pid="14" />
            <newsList :szywTitle="xzDynamicTitle" :newsList="xzDynamicList.slice(0, 8)" :id="25" :pid="14" />
            <newsList :szywTitle="dwDynamicTitle" :newsList="dwDynamicList.slice(0, 8)" :id="32" :pid="14" />
          </div>
          <!-- <div class="dwdt">
            <div class="dwdt-top">
              <div class="dwdt-title">单位动态</div>
              <div class="dwdt-more" @click="goNews(32, 14)">更多></div>
            </div>
            <ul>
              <li
                v-for="item in dwDynamicList.slice(0, 9)"
                :key="item.id"
                @click="goDetail(item.id)"
              >
                <span>{{ item.news_title }}</span>
              </li>
            </ul>
          </div> -->
        </div>
        <div>
          <img class="bj" :src="beijing_image2" alt="" @click="go_blank2()" />
        </div>
        <div class="btwh">
          <div class="btwh-img">
            <!-- <img class="btwh-img" src="../../assets/btwh.png" alt="" /> -->
            <div class="bj">HUMANITIES</div>
            <div class="txt">固始人文</div>
          </div>
          <div class="btwh-item">
            <!-- <newsImgList :btwhTitle="exhibitionTitle" :newsImgList="exhibitionList" :id="5" :pid="18" /> -->
            <newsImgList :btwhTitle="relativeTitle" :newsImgList="relativeList" :id="12" :pid="18"></newsImgList>
            <newsImgList :btwhTitle="festivalTitle" :newsImgList="festivalList" :id="33" :pid="18"></newsImgList>
            <dzbList :newsImgList="dzbNewList"></dzbList>
          </div>
          <div class="btwh-down">
            <!-- <div class="sfq">
              <div class="more-sfq">
                <div class="sfq-title">四个固始</div>
                <div class="more-title" @click="goFourGushi()">更多></div>
              </div>
              <div class="sfq-item">
                <div class="sfq-name sxgs" @click="getSxgsList()">书香固始</div>
                <ul class="ul ul-active" ref="sx">
                  <li v-for="item in sxgsList" :key="item.id" @click="goDetail(item.id)">
                    <span v-if="item.news_title">
                      {{ item.news_title }}
                    </span>
                  </li>
                </ul>
                <div class="sfq-name jkgs" @click="getJkgsList()">健康固始</div>
                <ul class="ul" ref="jk">
                  <li v-for="item in jkgsList" :key="item.id" @click="goDetail(item.id)">
                    <span v-if="item.news_title">
                      {{ item.news_title }}
                    </span>
                  </li>
                </ul>
                <div class="sfq-name mlgs" @click="getMlgsList()">明理固始</div>
                <ul class="ul" ref="ml">
                  <li v-for="item in mlgsList" :key="item.id" @click="goDetail(item.id)">
                    <span v-if="item.news_title">
                      {{ item.news_title }}
                    </span>
                  </li>
                </ul>
                <div class="sfq-name cxgs" @click="getCxgsList()">诚信固始</div>
                <ul class="ul" ref="cx">
                  <li v-for="item in cxgsList" :key="item.id" @click="goDetail(item.id)">
                    <span v-if="item.news_title">
                      {{ item.news_title }}
                    </span>
                  </li>
                </ul>
              </div>
            </div> -->
            <!-- <div class="sfq1">
              <div class="sfq1-top">
                <div class="sfq1-top-more">
                  <div class="sfq-title">四个固始</div>
                  <div class="more-title1" @click="goFourGushi()">更多></div>
                </div>
                <div class="top-item">
                  <div v-for="(item, index) in sfqList" :key="index" @click="getFourGushiInfo1(item.id, index)"
                    :class="index == current ? 'sfq1-actice' : ''">
                    {{ item.name }}
                  </div>
                  <div class="more-title" @click="goFourGushi()">更多></div>
                </div>
              </div>
              <div class="sfq-item">
                <ul>
                  <li v-for="item in sxgsList" :key="item.id" @click="goDetail(item.id)">
                    <span v-if="item.news_title">
                      {{ item.news_title }}
                    </span>
                  </li>
                </ul>
              </div>
            </div> -->
            <div class="service" style="width: 100%;">
              <div class="service-title">便民服务</div>
              <div class="service-item">
                <el-button type="text" @click="open(item)" v-for="item in convenienceList" :key="item.id">
                  {{ item.name }}
                </el-button>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <img class="bj" :src="beijing_image3" alt="" @click="go_blank3()" />
        </div>

        <div class="zjgs">
          <div class="zjgs-img">
            <!-- <img class="zjgs-img" src="../../assets/btwh.png" alt="" /> -->
            <div class="bj">NEAR</div>
            <div class="txt">走进固始</div>
          </div>
          <div class="zjgs-box-item">
            <div class="zjgs-box">
              <!-- <div class="zjgs-box-active">固始美景</div>
            <div>固始美食</div>
            <div>名人轶事</div>
            <div>绿色农产品</div>
            <div>城市风光</div> -->
              <div v-for="(item, index) in intoGushi" :key="item.id" @click="getIntoGuShi(index, item.id)"
                :class="intoGushiIndex == index ? 'zjgs-box-active' : ''">
                {{ item.name }}
              </div>
            </div>
          </div>

          <div class="zjgs-b" v-if="intoGushiList.length > 0 || intoGushiImg">
            <div class="zjgs-b-l" @click="goDetail(intoGushiImg.id, intoGushiImg.gory_id)">
              <template v-if="intoGushiImg">
                <img :src="intoGushiImg.news_image[0]" alt="" />
              </template>

              <div class="zjgs-bj">
                <div class="zjgs-name">

                  <span v-if="intoGushiImg.news_title">
                    {{ intoGushiImg.news_title }}
                  </span>
                </div>
                <div class="zjgs-desc">
                  {{ intoGushiImg.news_titleshort }}
                </div>
              </div>
            </div>
            <div class="zjgs-b-r">
              <div class="zjgs-b-r-box" v-for="item in intoGushiList" :key="item.id"
                @click="goDetail(item.id, item.gory_id)">
                <div class="img-top">
                  <img :src="item.news_image[0]" alt="" />
                </div>
                <div class="zjgs-b-r-b">
                  <div class="zjgs-b-r-name">
                    <span v-if="item.news_title">
                      {{ item.news_title }}
                    </span>
                  </div>
                  <div class="zjgs-b-r-desc">
                    {{ item.news_titleshort }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <el-empty :image-size="200"></el-empty>
          </div>
          <!-- <div class="zjgs-item">
            <div class="zjgs-list">
              <div class="zjgs-title">视听固始</div>
            </div>
            <div class="zjgs-list">
              <div class="zjgs-title">固始人文</div>
              <ul>
                <li>
                  <router-link :to="{ path: `/news/${12}/${18}` }"
                    >根亲文化</router-link
                  >
                  &nbsp;|&nbsp;
                </li>
                <li>
                  <router-link :to="{ path: `/news/${5}/${18}` }"
                    >文化节展</router-link
                  >&nbsp;|&nbsp;
                </li>
                <li>
                  <router-link :to="{ path: `/news/${12}/${18}` }"
                    >固始人文</router-link
                  >&nbsp;|&nbsp;
                </li>
                <li>
                  <router-link :to="{ path: `/news/${33}/${18}` }"
                    >网络中国节</router-link
                  >
                </li>
                <li>
                  <router-link :to="{ path: `/news/${40}/${18}` }"
                    >名人轶事</router-link
                  >&nbsp;|&nbsp;
                </li>
                <li>
                  <router-link :to="{ path: `/news/${39}/${18}` }"
                    >非遗文化</router-link
                  >
                </li>
              </ul>
            </div>
            <div class="zjgs-list">
              <div class="zjgs-title">固始历史</div>
              <ul>
                <li @click="toguide('gushihistory')">历史人物&nbsp;|&nbsp;</li>
                <li>历史事件</li>
              </ul>
            </div>
            <div class="zjgs-list">
              <div class="zjgs-title">固始风光</div>
              <ul>
                <li>
                  <router-link :to="{ path: '/gushibeauty' }"
                    >旅游景点</router-link
                  >
                  &nbsp;|&nbsp;
                </li>
                <li>图说固始&nbsp;|&nbsp;</li>
                <li>城市风光</li>
              </ul>
            </div>
            <div class="zjgs-list">
              <div class="zjgs-title">固始生活</div>
              <ul>
                <li>
                  <router-link :to="{ path: '/localcuisine' }"
                    >固始美食</router-link
                  >
                  &nbsp;|&nbsp;
                </li>
                <li>健康生活&nbsp;|&nbsp;</li>
                <li>商超</li>
              </ul>
            </div>
            <div class="zjgs-list">
              <div class="zjgs-title">民生服务</div>
              <ul>
                <li>招聘&nbsp;|&nbsp;</li>
                <li>资讯</li>
                <li>养老</li>
              </ul>
            </div>
          </div> -->
        </div>
      </div>
    </div>
    <!-- <div style="position: fixed;right: 20px;bottom: 30%;cursor: pointer;" @click="openUrl(2)">
      <img src="../../assets/left1.jpg" style="width: 100px;"></img>
    </div>  -->
    <Footer></Footer>
  </div>
</template>
<script>
import Swiper from 'swiper'
import newsList from '@/components/newsList/newsList.vue'
import newsImgList from '@/components/newsImgList/newsImgList.vue'
import Footer from '@/components/footers/footers.vue'
import dzbList from '@/components/dzb/index.vue'
import {
  navigation,
  currentPolitics,
  banner,
  recommend,
  service,
  beijing, rightTop, currentPoliticsNew, getDzb
} from '@/api/api.js'

export default {
  name: 'index',
  data() {
    return {
      serCounte: '',
      current: 0,
      sfqList: [],
      navList: [], //导航
      mediaGushiList: [], //媒体看固始
      mediaGushiTitle: '', //媒体看固始
      gushiNewsList: [], //固始新闻
      gushiNewsTitle: '', //媒体看固始
      xzDynamicList: [], //乡镇动态
      xzDynamicTitle: '', //媒体看固始
      dwDynamicList: [], //单位动态
      dwDynamicTitle: '', //媒体看固始
      exhibitionList: [], //文化节展
      exhibitionTitle: '', //媒体看固始
      relativeList: [], //根亲文化
      relativeTitle: '', //媒体看固始
      festivalList: [], //网络中国节
      festivalTitle: '', //媒体看固始
      featureList: [], //专题专栏
      featureTitle: '', //媒体看固始
      hotList: [], //头条
      recommemdList: [], //推荐
      newList: [], //轮播
      indexList: [], //首页头条
      countSzyw: 0,
      countZtzl: 0,
      fourList: [],
      sxgsList: [], //书香固始
      jkgsList: [], //健康固始
      mlgsList: [], //明理固始
      cxgsList: [], //诚信固始
      convenienceList: [],
      intoGushi: [
        {
          id: 3,
          name: '固始美景',
        },
        {
          id: 8,
          name: '固始美食',
        },
        {
          id: 40,
          name: '名人轶事',
        },
        {
          id: 21,
          name: '绿色农产品',
        },
        {
          id: 10,
          name: '城市风光',
        },
      ],
      intoGushiIndex: 0,
      intoGushiList: [],
      intoGushiImg: '',
      drawer: false,
      registerText: true,
      szywTwoList: [],
      gsysTwoList: [],

      screenWidth: null,
      beijing_image1: '',
      beijing_image2: '',
      beijing_image3: '',
      url1: '',
      url2: '',
      url3: '',
      flag: false,
      flag1: false,
      kaoshi: {},
      webIsPc: 1,
      topBan: false,
      dzbNewList: []
    }
  },
  mounted() {
    this.getNavigation()
    this.getSzyw()
    this.getGsrw()
    this.getFeature()
    this.getBannerHot()
    this.getBannerRecommemd()
    this.getBannerNew()
    //this.getFourGushi()
    //this.getFourGushiInfo(51, 0)
    //this.getFourGushiInfo1(51, 0)
    //this.getSxgs()
    //this.getJkgs()
    //this.getMlgs()
    //this.getCxgs()
    this.getInto(3)
    this.getIndexList()
    this.getService()
    this.getGsys()
    this.getRightTwo()
    this.get_dzb_list();
    // this.szyw_sz();

    const token = this.$store.getters.getToken
    if (token !== '') {
      this.registerText = false
    } else {
      this.registerText = true
    }

    this.screenWidth = document.body.clientWidth
    window.onresize = () => {
      return (() => {
        this.screenWidth = document.body.clientWidth
      })()
    }
  },
  destroyed() {
    window.onresize = null
  },
  computed: {},
  watch: {
    screenWidth: function (n, o) {
      if (n > 750) {
        this.webIsPc = 1;
        console.log('大于1200')
        this.getPcBj()
      } else if (n <= 750) {
        this.webIsPc = 2;
        console.log('小于1200')
        this.getBj()
      }
    },
  },
  methods: {
    openUrl(type) {
      //违法和不良信息举报入口
      if (type == 1) {
        window.open("https://www.xyxww.com.cn/jhtml/gn/150465.html");
      } else {
        window.open("https://piyao.xyxww.com.cn/");
      }
    },
    getRightTwo() {
      //考试公告
      rightTop('1001392').then((res) => {
        this.kaoshi = res.data.data
      })
      //招聘信息
      rightTop('1001391').then((res) => {
        this.zhaopin = res.data.data
      })
    },
    open_web(type) {
      if (type == 2) {
        window.open(this.kaoshi.vrchangyou)
      } else {
        window.open(this.zhaopin.vrchangyou)
      }
    },
    get_dzb_list() {
      getDzb({}).then((res) => {
        console.log(res);
        this.dzbNewList = res.data.data;
      })
    },
    getBj() {
      beijing(0, 2).then((res) => {
        this.beijing_image1 = res.data.data.beijing_image
        if (res.data.data.url !== '') {
          this.url1 = res.data.data.url
          // window.open(this.url1, "_blank");
        }
      })
      beijing(1, 2).then((res) => {
        this.beijing_image2 = res.data.data.beijing_image
        if (res.data.data.url !== '') {
          this.url2 = res.data.data.url
          // window.open(this.url2, "_blank");
        }
      })
      beijing(2, 2).then((res) => {
        this.beijing_image3 = res.data.data.beijing_image
        if (res.data.data.url !== '') {
          this.url3 = res.data.data.url
          // window.open(this.url3, "_blank");
        }
      })
    },
    getPcBj() {
      beijing(0, 1).then((res) => {
        this.beijing_image1 = res.data.data.beijing_image
        if (res.data.data.url !== '') {
          this.url1 = res.data.data.url
          // window.open(this.url1, "_blank");
        }
      })
      beijing(1, 1).then((res) => {
        this.beijing_image2 = res.data.data.beijing_image
        if (res.data.data.url !== '') {
          this.url2 = res.data.data.url
          // window.open(this.url2, "_blank");
        }
      })
      beijing(2, 1).then((res) => {
        this.beijing_image3 = res.data.data.beijing_image
        if (res.data.data.url !== '') {
          this.url3 = res.data.data.url
          // window.open(this.url3, "_blank");
        }
      })
    },
    go_blank3() {
      if (this.url3 !== '') {
        window.open(this.url3, '_blank')
      }
    },
    go_blank2() {
      // if (this.url2 !== "") {
      //   window.open(this.url2, "_blank");
      // }
      this.$router.push({
        name: 'news',
        params: {
          id: 43,
          pid: 13
        }
      })
    },
    go_blank1() {
      // if (this.url1 !== "") {
      //   window.open(this.url1, "_blank");
      // }
      this.$router.push({
        name: 'news',
        params: {
          id: 41,
          pid: 13
        }
      })
    },
    open(item) {
      this.$alert(item.phone, item.name, {
        confirmButtonText: '确定',
        callback: (action) => {
        },
      })
    },
    initSwiper1() {
      let that = this
      let mySwiper = new Swiper('.swiper1', {
        loop: true, // 循环模式选项
        // 如果需要分页器
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
        },
        autoplay: {
          delay: 3000, //1秒切换一次
        },
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        on: {
          click: function () {
            if (this.clickedSlide) {
              let item = JSON.parse(
                this.clickedSlide.attributes['data-href'].nodeValue
              )
              that.goDetail(item)
            }
          },
        },
      })
      //鼠标覆盖停止自动切换
      mySwiper.el.onmouseover = function () {
        mySwiper.autoplay.stop()
      }

      //鼠标离开开始自动切换
      mySwiper.el.onmouseout = function () {
        mySwiper.autoplay.start();
      };
    },
    initSwiper5() {
      let that = this
      new Swiper('.swiper5', {
        loop: true, // 循环模式选项
        effect: 'coverflow',
        grabCursor: true,
        centeredSlides: true,
        slidesPerView: 'auto',
        coverflow: {
          rotate: 0, //slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 100, //每个slide之间的拉伸值，越大slide靠得越紧。 默认0。
          depth: 115, //slide的位置深度。值越大z轴距离越远，看起来越小。 默认100。
          slideShadows: true, //开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: '.yidong2',
          prevEl: '.yidong1',
        },
        on: {
          click: function () {
            let item = JSON.parse(
              this.clickedSlide.attributes['data-href'].nodeValue
            )
            that.goNews(item, 13)
          },
        },
        // observer: true, //修改swiper自己或子元素时，自动初始化swiper
        // observeParents: true, //修改swiper的父元素时，自动初始化swiper
      })
    },
    initSwiper2() {
      let that = this
      let mySwiper = new Swiper('.swiper2', {
        loop: true, // 循环模式选项
        slidesPerView: 'auto',
        // autoplay: {
        //   delay: 3000, //1秒切换一次
        // },
        navigation: {
          nextEl: '.el-icon-arrow-right',
          prevEl: '.el-icon-arrow-left',
        },
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        slideVisibleClass: 'mysad',
        watchSlidesProgress: true,
        watchSlidesVisibility: true,
        // spaceBetween: 20,
        on: {
          click: function () {

            let item = JSON.parse(
              this.clickedSlide.attributes['data-href'].nodeValue
            )
            if (item.diyname != '') {
              window.open(item.diyname)
              return
            }
            that.goNews(item.id, 13)
          },
        },
      })
      //鼠标覆盖停止自动切换
      // mySwiper.el.onmouseover = function () {
      //   mySwiper.autoplay.stop();
      // };

      // //鼠标离开开始自动切换
      // mySwiper.el.onmouseout = function () {
      //   mySwiper.autoplay.start();
      // };
    },
    curShow(index) {
      // if (index !== 3) {
      //   if (this.current == index) {
      //     this.current = -1;
      //   } else {
      //     this.current = index;
      //   }
      // } else {
      //   this.current = index;
      // }
      if (this.current !== index) {
        this.current = index
      }
    },
    //banner
    getBannerHot() {
      banner(0, 'hot').then((res) => {
        this.hotList = res.data.data[0]
      })
    },
    getIndexList() {
      banner(0, 'index').then((res) => {
        this.indexList = res.data.data
      })
    },
    getBannerRecommemd() {
      // banner(0, "recommend").then((res) => {
      //
      //   this.recommemdList = res.data.data.slice(0, 8);
      // });
      recommend().then((res) => {
        this.recommemdList = res.data.data.slice(0, 8)
      })
    },
    // getPrefix(id) {
    //   currentPolitics(id).then((res) => {
    //
    //   });
    // },
    getBannerNew() {
      banner(0, 'new').then((res) => {
        this.newList = res.data.data.slice(0, 4)
        this.$nextTick(() => {
          this.initSwiper1()
        })
      })
    },
    //导航
    getNavigation() {
      navigation().then((res) => {
        this.navList = res.data.data
      })
    },
    //时政要闻
    getSzyw() {
      navigation(14).then((res) => {
        this.szywTwoList = res.data.data
        this.countSzyw = res.data.data[0]
        res.data.data.forEach((item) => {
          this.getCurrentPolitics(item.id)
        })
      })
    },
    //固始营商
    getGsys() {
      navigation(6).then((res) => {
        this.gsysTwoList = res.data.data
      })
    },
    getCurrentPolitics(id) {
      var that = this
      currentPoliticsNew(id, 1, 8).then((res) => {
        switch (id) {
          case 27:
            that.mediaGushiTitle = res.data.data.name
            that.mediaGushiList = res.data.data.news
            break
          case 26:
            that.gushiNewsTitle = res.data.data.name
            that.gushiNewsList = res.data.data.news
            break
          case 32:
            that.dwDynamicTitle = res.data.data.name
            that.dwDynamicList = res.data.data.news
            break
          case 25:
            that.xzDynamicTitle = res.data.data.name
            that.xzDynamicList = res.data.data.news
            break
          // case 5:
          //   that.exhibitionTitle = res.data.data.name
          //   that.exhibitionList = res.data.data.news
          //   break
          case 12:
            that.relativeTitle = res.data.data.name
            console.log(res.data.data.news)
            var yourArray = res.data.data.news
            // 使用filter()方法来删除news_image为空的元素
            var filteredArray = yourArray.filter(function (item) {
              return item.news_image !== ''
            })
            // 如果需要重排数组，你可以使用sort()方法
            filteredArray.sort(function (a, b) {
              // 这里可以根据你的排序需求进行比较
              // 例如，按content属性升序排序
              return a.id - b.id
            })
            that.relativeList = filteredArray
            break
          case 33:
            that.festivalTitle = res.data.data.name
            that.festivalList = res.data.data.news
            break
        }
      })
    },
    //固始人文
    getGsrw() {
      navigation(18).then((res) => {
        res.data.data.forEach((item) => {
          this.getCurrentPolitics(item.id)
        })
      })
    },
    //专题专栏
    getFeature() {
      navigation(13).then((res) => {
        this.countZtzl = res.data.data[0]
        this.featureList = res.data.data
        this.$nextTick(() => {
          this.initSwiper2()
          this.initSwiper5()
        })
      })
    },
    //详情
    goDetail(id, gory_id) {
      if (gory_id == 3) {
        this.$router.push({
          path: `/scenicdetails/${id}`,
        })
      } else if (gory_id == 8) {
        this.$router.push({
          path: `/gourmetdetails/${id}`,
        })
      } else {
        this.$router.push({
          path: `/newsdetail/${id}`,
        })
      }
    },
    //新闻列表
    goNews(id, pid) {
      this.$router.push({
        path: `/news/${id}/${pid}`,
      })
    },
    fun_str(item) {
      return JSON.stringify(item)
    },
    dianji() {
      console.log(1);
      this.topBan = !this.topBan;
    },
    //四个固始
    getFourGushi() {
      navigation(50).then((res) => {
        this.sfqList = res.data.data
      })
    },
    getFourGushiInfo(id, index) {
      if (this.current !== index) {
        this.current = index
      }
      currentPolitics(id).then((res) => {
        this.fourList = res.data.data.news.slice(0, 6)
      })
    },
    getFourGushiInfo1(id, index) {
      this.current = index
      currentPolitics(id).then((res) => {
        this.fourList = res.data.data.news.slice(0, 6)
      })
    },
    //固始人文
    goNewsList(id, pid) {
      this.$router.push({
        path: `/news/${id}/${pid}`,
      })
    },
    //固始历史
    toguide(id) {
      let select = id
      localStorage.setItem('id', select)
      this.$router.push({ path: '/charmgushi' })
    },
    focuWidth() {
      // let inp = document.getElementById("search-inp");
      // inp.style.width = "250px";
      this.$router.push({
        name: 'search',
      })
    },
    blurWidth() {
      let inp = document.getElementById('search-inp')
      inp.style.width = '120px'
    },
    goSearch() {
      //let inp = document.getElementById('search-inp')
      //let text = inp.value
      this.$router.push({ path: `/search/?key=${this.serCounte}` })
    },
    goSearch2() {
      let inp = document.getElementById('search-inp2')
      let text = inp.value
      this.$router.push({
        name: 'search',
        params: {
          name: text,
        },
      })
    },
    //书香固始
    getSxgs() {
      currentPolitics(51).then((res) => {
        this.sxgsList = res.data.data.news.slice(0, 6)
      })
    },
    getSxgsList() {
      this.$refs.sx.style.flex = '1'
      this.$refs.sx.style.padding = '10px 25px'
      this.$refs.jk.style.flex = '0'
      this.$refs.jk.style.padding = '0'
      this.$refs.ml.style.flex = '0'
      this.$refs.ml.style.padding = '0'
      this.$refs.cx.style.flex = '0'
      this.$refs.cx.style.padding = '0'
      this.getSxgs();
    },
    //健康固始
    getJkgs() {
      currentPolitics(52).then((res) => {
        this.jkgsList = res.data.data.news.slice(0, 6)
      })
    },
    getJkgsList() {
      this.$refs.sx.style.flex = '0'
      this.$refs.sx.style.padding = '0'
      this.$refs.jk.style.flex = '1'
      this.$refs.jk.style.padding = '10px 25px'
      this.$refs.ml.style.flex = '0'
      this.$refs.ml.style.padding = '0'
      this.$refs.cx.style.flex = '0'
      this.$refs.cx.style.padding = '0'
      this.getJkgs();
    },
    //明理固始
    getMlgs() {
      currentPolitics(53).then((res) => {
        this.mlgsList = res.data.data.news.slice(0, 6)
      })
    },
    getMlgsList() {
      this.$refs.sx.style.flex = '0'
      this.$refs.sx.style.padding = '0'
      this.$refs.jk.style.flex = '0'
      this.$refs.jk.style.padding = '0'
      this.$refs.ml.style.flex = '1'
      this.$refs.ml.style.padding = '10px 25px'
      this.$refs.cx.style.flex = '0'
      this.$refs.cx.style.padding = '0'
      this.getMlgs();
    },
    //诚信固始
    getCxgs() {
      currentPolitics(54).then((res) => {
        this.cxgsList = res.data.data.news.slice(0, 6)
      })
    },
    getCxgsList() {
      this.$refs.sx.style.flex = '0'
      this.$refs.sx.style.padding = '0'
      this.$refs.jk.style.flex = '0'
      this.$refs.jk.style.padding = '0'
      this.$refs.ml.style.flex = '0'
      this.$refs.ml.style.padding = '0'
      this.$refs.cx.style.flex = '1'
      this.$refs.cx.style.padding = '10px 25px'
      this.getCxgs();
    },
    //走进固始
    getIntoGuShi(index, id) {
      this.intoGushiIndex = index
      this.getInto(id)
    },
    getInto(id) {
      currentPolitics(id).then((res) => {
        console.log(res)
        this.intoGushiList = res.data.data.news.slice(1, 5)
        this.intoGushiImg = res.data.data.news[0]
      })
    },
    //便民服务
    getService() {
      service().then((res) => {
        this.convenienceList = res.data.data
      })
    },
    goFourGushi() {
      this.$router.push({ path: `/gushiys/${50}` })
    },
    goLogin() {
      const token = this.$store.getters.getToken
      if (token !== '') {
        this.$router.push({
          name: 'user',
        })
      } else {
        this.$router.push({
          name: 'login',
        })
      }
    },
    goZhuce() {
      this.$router.push({
        name: 'register',
      })
    },
    goLiuyan() {
      this.$router.push({
        name: 'user',
      })
    },

    goRegister() {
      this.$store.commit('delToken')
      window.location.reload()
    },
    szyw_sz() {
      // let szyw_sz11 = document.getElementsByClassName("two-item1")[0];
      // let sz = document.getElementsByClassName("sz")[0];
      // sz.style.transform = "rotate(90deg)";
      // szyw_sz11.style.height = "max-content";
      this.$('.two-item1').toggle(300)
      this.$('.two-item2').hide(300)
      this.flag1 = !this.flag1
      // this.$(".sz").click(function () {
      //   this.$(".two-item1").fadeToggle();
      // });
    },
    ysgs_ys() {
      this.$('.two-item2').toggle(300)
      this.$('.two-item1').hide(300)
      this.flag = !this.flag
    },
  },
  components: {
    newsList,
    newsImgList,
    Footer,
    dzbList
  },
}
</script>
<style>
.el-drawer__open .el-drawer.rtl {
  width: 50% !important;
  padding: 30px;
}

@media screen and (max-width: 420px) {
  .el-message-box {
    width: 200px !important;
  }
}
</style>
<style scoped lang="scss">
// .ipv6 {
//   &:hover {
//     color: #bf0614;
//   }
// }
.wza {
  &:hover {
    color: #bf0614;
  }
}

.login-item {
  &:hover {
    color: #bf0614;
  }
}

.liuyan {
  &:hover {
    color: #bf0614;
  }
}

.zhuce {
  &:hover {
    color: #bf0614;
  }
}

.index {
  background: #f7f7f7;
}

.drawer {
  background: #fff;
  text-align: left;
  font-size: 24px;
  font-weight: 400;
  color: #808080;

  div {
    cursor: pointer;
  }

  a {
    font-size: 24px;
    font-weight: 400;
    color: #808080;

    &:hover {
      color: #bf0614;
    }
  }

  .search {
    display: flex;
    align-items: center;
    margin-top: 8px;

    input {
      height: 26px;

      margin-right: 10px;
    }
  }
}

nav {
  background: #ffffff;
  border-bottom: 1px solid #bf0614;
  height: 120px;
  width: 100%;

  .nav-box {
    position: fixed;
    top: 0;
    z-index: 999999;
    background: #ffffff;
    height: 120px;
    width: 100%;
  }

  .nav-item {
    margin: 0 auto;
    max-width: 1420px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .nav-right1 {
      position: absolute;
      // margin-top: 20px;
      z-index: 9999;
      height: 100vh;
      width: 50%;
      right: 0;
      top: 70px;
      background: #eee;
      text-align: left;
      padding: 10px 20px 0;
      font-size: 16px;
      font-weight: 400;
      color: #666;

      // line-height: 1.5;
      .wza {
        border-top: 1px #ddd solid;
        height: 40px;
        line-height: 40px;
      }

      .login-item {
        border-top: 1px #ddd solid;
        height: 40px;
        line-height: 40px;
      }

      .zhuce {
        border-top: 1px #ddd solid;
        height: 40px;
        line-height: 40px;
        border-bottom: 1px #ddd solid;
      }

      .liuyan {
        border-top: 1px #ddd solid;
        height: 40px;
        line-height: 40px;
        border-bottom: 1px #ddd solid;
      }

      .nav-title {
        a {
          font-size: 16px;
          font-weight: 400;
          color: #666;
        }

        ul {
          li {
            // padding: 5px 0;
            // line-height: 1.5;
            border-top: 1px #ddd solid;
            height: 40px;
            line-height: 40px;
            overflow: hidden;

            &:nth-child(1) {
              border-top: none;
            }
          }

          #szyw_sz1 {
            height: max-content;
          }

          #szyw_sz2 {
            height: max-content;
          }

          .nav-title-icon {
            display: flex;
            justify-content: space-between;
            align-items: center;

            div {
              padding: 0 8px;
            }
          }

          .two-item {
            padding-left: 15px;
            box-sizing: border-box;

            li {
              color: #999;

              &:nth-last-child(1) {
                padding: 5px 0 0 0;
              }
            }
          }

          .two-item1 {
            display: none;
          }

          .two-item2 {
            display: none;
          }
        }
      }

      .search {
        width: 100%;
        height: 30px;
        position: relative;

        input {
          width: 100%;
          height: 30px;
          background: #e6e6e6;
          border-radius: 15px;
          border: none;
          outline: none;
          font-size: 16px;
          font-weight: 400;
          color: #808080;
          padding: 0 30px 0 14px;
          box-sizing: border-box;
        }

        img {
          width: 16px;
          height: 16px;
          position: absolute;
          top: 11px;
          right: 12px;
          cursor: pointer;
          // color: #808080;
        }
      }
    }

    .el-icon-close {
      display: none;
    }

    .el-icon-more {
      display: none;
    }

    .logo {
      height: 60px;
      width: auto;

      img {
        // width: 132px;
        height: 70px;
        margin-top: 6px;
      }
    }

    .nav-right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .nav-right-top {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 400;
        color: #808080;

        div {
          cursor: pointer;
        }

        .ipv6 {
          padding: 4px 12px;
          border: 1px solid #808080;
          border-radius: 100px;
          margin-right: 10px;
        }

        .login {
          display: flex;
          align-items: center;

          div {
            &:hover {
              color: #bf0614;
            }
          }
        }
      }

      .nav-title {
        display: flex;

        ul {
          display: flex;
          font-size: 24px;
          font-weight: 400;
          color: #333333;

          a {
            font-size: 24px;
            font-weight: 400;
            color: #ffffff;
          }

          li {
            cursor: pointer;
            color: #FFFFFF;
            padding: 20px;
            width: 160px;
            text-align: center;

            &:nth-last-child(1) {
              margin-right: 31px;
            }

            &:hover {
              color: #ffffff;
              background-color: #D50616;
            }

            &:hover a {
              color: #ffffff;
            }
          }
        }

        .search {
          width: 120px;
          height: 30px;
          position: relative;

          input {
            width: 120px;
            height: 30px;
            background: #e6e6e6;
            border-radius: 15px;
            border: none;
            outline: none;
            font-size: 16px;
            font-weight: 400;
            color: #808080;
            padding-left: 14px;
            padding-right: 30px;
            box-sizing: border-box;
            transition: 1s;
            position: absolute;
            right: 0;
          }

          img {
            width: 16px;
            height: 16px;
            position: absolute;
            top: 7px;
            right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .self {
      display: none;
    }
  }
}

.content {
  padding: 0 0 40px;

  .banner_ {
    width: 100%;
    height: 574px;
    position: relative;
  }

  .content-bj {
    width: 100%;
    height: 574px;
    object-fit: cover;
    top: 0;
    left: 0;
    display: block;
    position: absolute;
  }

  .cbl {
    position: fixed;
    right: 0;
    z-index: 99;
    display: flex;
    flex-direction: column;
    top: 40%;

    .cbl-b {
      width: 150px;
      height: 48px;
      background: #bf0614;
      font-size: 20px;
      font-weight: 400;
      color: #ffffff;
      text-align: center;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #fff;

      &:hover {
        color: #bf0614;
        background: #fff;
      }

      &:hover img {
        background: #bf0614;
      }

      &:nth-last-child(1) {
        border-bottom: none;
      }

      img {
        width: 16px;
        height: 16px;
      }

      .cbl-name {
        margin-left: 8px;
      }
    }
  }

  .content-item {
    max-width: 1420px;
    margin: 20px auto 0;
  }

  .bj {
    max-width: 1420px;
    // height: 119px;
    display: block;
    object-fit: cover;
    width: 100%;
    cursor: pointer;
  }

  .news {
    text-align: center;
    padding: 0 80px;
    margin-top: 39px;

    .news-title {
      font-size: 2.5em;
      font-weight: bold;
      color: #bf0614;
      cursor: pointer;

      &:hover {
        color: #F4371B;
      }
    }

    .news-list {
      font-size: 16px;
      font-weight: bold;
      color: #808080;
      margin: 20px 0 60px;
      // display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；

      .news-list-name {
        // display: inline-block;
        // max-width: 30%;
        // overflow: hidden; //超出的文本隐藏
        // text-overflow: ellipsis; //溢出用省略号显示
        // white-space: nowrap; // 默认不换行；
        cursor: pointer;
        font-weight: 500;

        &:hover {
          color: #F4371B;
        }

        // &::after {
        //   content: "|";
        //   margin: 0 10px;
        // }

        // &:nth-last-child(1) {
        //   &::after {
        //     content: "";
        //   }
        // }
      }
    }
  }

  .banner {
    max-width: 1420px;
    height: 700px;
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    position: relative;
    background: #fff;

    .bj-box {
      width: 980px;
      height: 580px;
      background: #bf0614;
      position: absolute;
      top: -20px;
      left: -30px;
      box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
      z-index: 1;
    }

    .banner-item {
      display: flex;
      position: absolute;
      left: 0;
      z-index: 2;
      max-width: 1420px;
      padding: 30px 30px 0px 30px;

      .up-left {
        border-top: 20px solid #bf0614;
        border-right: 20px solid transparent;
        border-bottom: 20px solid transparent;
        border-left: 20px solid #bf0614;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 3;
      }

      .banner-item-news {
        // width: 570px;
        width: 45%;
        background: #ffffff;

        .news-title {
          height: 120px;
          margin-top: 10px;
          line-height: 48px;
          font-size: 32px;
          font-weight: bold;
          color: #333333;
          padding: 0 60px;
          text-align: center;
          overflow: hidden; //超出的文本隐藏
          display: -webkit-box;
          -webkit-line-clamp: 2; // 超出多少行
          -webkit-box-orient: vertical;
          border-bottom: 1px solid #cccccc;
          cursor: pointer;

          &:hover {
            color: #bf0614;
          }
        }

        ul {
          padding: 0 30px 0 0px;
          box-sizing: border-box;

          li {
            display: flex;
            line-height: 45px;
            cursor: pointer;
            transition: 0.5s;
            padding: 5px 0px;

            &:hover {
              transform: translateX(-10px);
            }

            span {
              font-size: 20px;
              font-weight: 400;
              display: inline-block;

              &:nth-child(1) {
                color: #bf0614;
              }

              &:nth-child(2) {
                flex: 1;
                color: #333333;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; // 默认不换行；

                &:hover {
                  color: #bf0614;
                }
              }
            }
          }
        }
      }

      #banner-item-news {
        display: none;
      }

      .swiper1 {
        // width: 820px;
        width: 768px;
        height: 430px;

        .swiper-slide {
          cursor: pointer;
        }

        ::v-deep .swiper-pagination {
          right: -42%;
          left: auto;
          bottom: 15px;
        }

        ::v-deep .swiper-pagination-bullet-active {
          background-color: #fff;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .swiper-title {
          font-size: 20px;
          font-weight: 400;
          color: #ffffff;
          height: 50px;
          background-color: rgba(0, 0, 0, 0.5);
          position: absolute;
          bottom: 0;
          width: 100%;
          padding: 0 146px 0 20px;
          line-height: 50px;
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; // 默认不换行；
        }
      }
    }

    .banner-bottom {
      position: absolute;
      bottom: 25px;
      left: 0;
      z-index: 4;
      height: 180px;
      padding: 0 30px;
      display: flex;
      align-items: center;
      width: 100%;
      // justify-content: space-between;
      width: 100%;

      .yidong1,
      .yidong2 {
        display: none;
      }

      .banner-icon {
        display: none;
        width: 60px;
        height: 60px;
        background: #cccccc;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 45px;
        font-weight: 700;
        color: #fff;
        cursor: pointer;
        flex-shrink: 0;
      }

      .banner-icon1 {
        margin-right: 30px;
      }

      .banner-icon2 {
        margin-left: 30px;
      }

      .swiper2 {
        max-width: 1120px;
        overflow: hidden;

        .swiper-slide {
          width: 280px;
          height: 180px;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            cursor: pointer;
            width: 260px;
            height: 160px;
            border-radius: 12px;
            box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
            -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
            -moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
            // -webkit-filter: drop-shadow(
            //   0px 0px 20px 0px rgba(0, 0, 0, 0.2)
            // ); /*考虑浏览器兼容性：兼容 Chrome, Safari, Opera */
            // filter: drop-shadow(0px 0px 20px 0px rgba(0, 0, 0, 0.2));
            display: block;
            object-fit: cover;
            transition: 0.5s;

            &:hover {
              transform: translate(-10px, -10px);
              /*向右移动100px,向下移动50px*/
            }
          }
        }
      }

      .swiper5 {
        max-width: 1120px;
        overflow: hidden;
        display: none;

        // .swiper-slide {
        //   width: 305px;
        //   height: 180px;
        //   display: flex;
        //   align-items: center;
        //   justify-content: center;
        //   img {
        //     cursor: pointer;
        //     width: 280px;
        //     height: 160px;
        //     border-radius: 12px;
        //     box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
        //     display: block;
        //     object-fit: cover;
        //   }
        // }
        .swiper-slide {
          text-align: center;
          font-size: 18px;
          // background: #eee;
          width: 305px;
          height: 180px;
          display: flex;
          flex-shrink: 0;
          justify-content: center;
          align-items: center;
          transition: 300ms;
          transform: scale(0.8);

          img {
            cursor: pointer;
            width: 280px;
            height: 160px;
            border-radius: 12px;
            // box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
            display: block;
            object-fit: cover;
          }
        }

        .swiper-slide-active,
        .swiper-slide-duplicate-active {
          transform: scale(1.1) !important;
        }
      }
    }
  }

  .szyw {
    margin-bottom: 40px;

    .szyw-img {
      margin: 57px auto 39px;
      height: 106px;
      position: relative;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      .bj {
        width: 258px;
        font-size: 80px;
        font-weight: bold;
        color: rgba(223, 58, 58, 0);
        background-image: linear-gradient(to right,
            rgba(191, 6, 20, 0.5) 0%,
            rgba(255, 255, 255, 0.5) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: absolute;
        left: 50%;
        margin-left: -129px;
        top: 0;
        text-align: center;
      }

      .txt {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        position: absolute;
        top: 50%;
        margin-top: -21px;
        left: 50%;
        margin-left: -64px;
      }
    }

    .szyw-item {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    .dwdt {
      max-width: 1420px;
      height: max-content;
      background: #ffffff;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
      margin-top: 20px;
      padding: 20px;

      .dwdt-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 23px 0 20px;
        background: #f2f2f2;
        height: 60px;

        .dwdt-title {
          font-size: 24px;
          font-weight: 400;
          color: #bf0614;
        }

        .dwdt-more {
          font-size: 16px;
          font-weight: 400;
          color: #808080;
          cursor: pointer;

          &:hover {
            color: #bf0614;
          }
        }
      }

      ul {
        font-size: 20px;
        font-weight: 400;
        color: #333333;
        list-style-type: disc;
        list-style-position: inside;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        margin-top: 10px;

        li {
          line-height: 45px;
          // margin-right: 70px;
          width: 460px;
          padding-left: 40px;
          box-sizing: border-box;
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; // 默认不换行；
          cursor: pointer;

          span {
            transition: 0.5s;

            &:hover {
              color: #bf0614;
              margin-left: -10px;
            }
          }

          &::marker {
            color: #bf0614;
          }

          &:nth-child(3n) {
            margin-right: 0;
          }
        }
      }
    }
  }

  .btwh {
    margin-bottom: 40px;

    .btwh-img {
      display: block;
      margin: 41px auto 39px;
      position: relative;
      height: 106px;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      .bj {
        width: 584px;
        font-size: 80px;
        font-weight: bold;
        color: rgba(223, 58, 58, 0);
        background-image: linear-gradient(90deg,
            rgba(191, 6, 20, 0.5) 0%,
            rgba(255, 255, 255, 0) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: absolute;
        left: 50%;
        margin-left: -292px;
        top: 0;
      }

      .txt {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        position: absolute;
        left: 50%;
        margin-left: -64px;
        top: 50%;
        margin-top: -21px;
      }
    }

    .btwh-item {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    .btwh-down {
      // margin-top: 20px;
      display: flex;
      justify-content: space-between;

      .more-sfq {
        width: 100%;
        display: flex;
        height: 40px;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #bfbeba;

        .more-title {
          font-size: 16px;
          font-weight: 400;
          color: #808080;
          cursor: pointer;

          &:hover {
            color: #bf0614;
          }
        }
      }

      // flex-wrap: wrap;
      .sfq {
        padding: 20px 20px 30px;
        width: 940px;
        height: 407px;
        background: #ffffff;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
        margin-top: 20px;
        margin-right: 20px;

        .sfq-name {
          width: 100px;
          height: 300px;
          font-size: 24px;
          font-weight: bold;
          color: #ffffff;
          writing-mode: vertical-lr;
          line-height: 100px;
          text-align: center;
          cursor: pointer;
          letter-spacing: 10px;
        }

        .ul {
          width: 0;
          border-top: 1px solid #bfbeba;
          border-bottom: 1px solid #bfbeba;
          box-sizing: border-box;
          // list-style-type: disc;
          // list-style-position: inside;
          overflow: hidden;
          transition: 0.5s;

          &:nth-last-child(1) {
            border-right: 1px solid #bfbeba;
          }

          li {
            // width: 100%;
            flex: 1;
            font-size: 20px;
            font-weight: 400;
            color: #333333;
            line-height: 45px;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; // 默认不换行；
            transition: 0.5s;
            cursor: pointer;

            &:hover {
              color: #bf0614;
              transform: translateX(-10px);
            }

            &::before {
              content: "";
              width: 6px;
              height: 6px;
              display: inline-block;
              border-radius: 6px;
              background: rgba(191, 6, 20, 1);
              margin-right: 10px;
              flex-shrink: 0;
              line-height: 45px;
              margin-bottom: 3px;
            }

            // &::marker {
            //   color: #bf0614;
            // }
          }
        }

        .ul-active {
          flex: 1;
          padding: 10px 25px;
        }

        .sxgs {
          background: url("../../assets/sxgs.png");
        }

        .jkgs {
          background: url("../../assets/jkgs.png");
        }

        .mlgs {
          background: url("../../assets/mlgs.png");
        }

        .cxgs {
          background: url("../../assets/cxgs.png");
        }

        .sfq-title {
          font-size: 24px;
          font-weight: 400;
          color: #bf0614;
          height: 40px;
          border-bottom: 1px solid #bfbeba;

          &::after {
            content: "";
            display: block;
            background: #bf0614;
            height: 3px;
            width: 110px;
            margin-top: 6px;
          }
        }
      }

      .sfq1 {
        display: none;
        padding: 20px 20px 30px;
        width: 100%;
        height: 407px;
        background: #ffffff;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
        margin-top: 20px;

        .sfq1-actice {
          color: #bf0614 !important;
        }

        .sfq1-top {
          border-bottom: 1px solid #bfbeba;
          display: flex;
          justify-content: space-between;

          .more-title1 {
            display: none;
          }

          .sfq-title {
            font-size: 24px;
            font-weight: 400;
            color: #bf0614;
            height: 40px;

            &::after {
              content: "";
              display: block;
              background: #bf0614;
              height: 3px;
              width: 110px;
              margin-top: 6px;
            }
          }

          .top-item {
            display: flex;

            div {
              font-size: 20px;
              font-weight: 400;
              color: #808080;
              cursor: pointer;
              margin-right: 30px;
            }

            .more-title {
              margin-right: 0;
              font-size: 16px;
              margin-top: 4px;
            }

            .top-item-active {
              color: #bf0614;
            }
          }
        }

        .sfq-item {
          margin-top: 20px;
          display: flex;

          ul {
            list-style-type: disc;
            list-style-position: inside;
            font-size: 20px;
            font-weight: 400;
            color: #333333;
            padding-left: 25px;
            box-sizing: border-box;
            padding-top: 10px;
            transition: all ease 0.5s;
            overflow: hidden;

            li {
              width: 100%;
              line-height: 45px;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
              box-sizing: border-box;
              cursor: pointer;

              &::marker {
                color: #bf0614;
                margin-right: 10px;
              }

              &:hover {
                color: #bf0614;
              }
            }
          }

          .actice-sfq {
            width: 0;
            padding-left: 0;

            &:nth-last-child(1) {
              display: block;
            }
          }
        }
      }

      .sfq-item {
        margin-top: 20px;
        display: flex;

        .sfq-item-box {
          display: flex;

          &:nth-last-child(1) {
            border-right: 1px solid #bfbeba;
          }

          div {
            width: 100px;
            height: 300px;
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
            writing-mode: vertical-lr;
            text-align: center;
            line-height: 100px;
            cursor: pointer;
          }

          .sxgs {
            background-size: 100% 100%;
          }

          ul {
            list-style-type: disc;
            list-style-position: inside;
            font-size: 20px;
            font-weight: 400;
            color: #333333;
            padding-left: 25px;
            box-sizing: border-box;
            width: 500px;
            height: 300px;
            border-top: 1px solid #bfbeba;
            border-bottom: 1px solid #bfbeba;
            padding-top: 10px;
            transition: all ease 0.5s;
            overflow: hidden;

            li {
              width: 440px;
              line-height: 45px;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
              box-sizing: border-box;
              cursor: pointer;

              &::marker {
                color: #bf0614;
                margin-right: 10px;
              }

              &:hover {
                color: #bf0614;
              }
            }
          }

          .actice-sfq {
            width: 0;
            padding-left: 0;

            &:nth-last-child(1) {
              display: block;
            }
          }
        }
      }
    }

    .service {
      width: 460px;
      height: max-content;
      background: #ffffff;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
      padding: 20px 20px 27px;
      margin-top: 20px;
      flex-shrink: 0;

      .service-title {
        text-align: center;
        font-size: 24px;
        font-weight: 400;
        color: #bf0614;
        height: 40px;
        border-bottom: 1px solid #bfbeba;

        &::after {
          content: "";
          display: block;
          background: #bf0614;
          height: 3px;
          width: 110px;
          margin: 0 auto;
          margin-top: 6px;
        }
      }

      .service-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 20px;
        gap: 10px;

        a {
          display: block;
          font-size: 20px;
          font-weight: 400;
          color: #333333;
          line-height: 73px;
          width: 133px;
          height: 50px;
          background: #ebebeb;
          text-align: center;
          line-height: 50px;
          margin-top: 10px;

          &:hover {
            color: #bf0614;
          }
        }

        .el-button--text {
          display: block;
          margin-left: 0;
          font-size: 20px;
          font-weight: 400;
          color: #333333;
          line-height: 73px;
          width: 133px;
          height: 50px;
          background: #ebebeb;
          text-align: center;
          line-height: 1;
          margin-top: 10px;

          &:hover {
            color: #bf0614;
          }
        }

        div {
          width: 133px;
          height: 0;
        }
      }
    }
  }

  .zjgs {
    .zjgs-img {
      display: block;
      margin: 41px auto 40px;
      height: 106px;
      position: relative;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      .bj {
        width: 242px;
        font-size: 80px;
        font-weight: bold;
        color: rgba(223, 58, 58, 0);
        background: linear-gradient(90deg,
            rgba(191, 6, 20, 0.5) 0%,
            rgba(255, 255, 255, 0.5) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: absolute;
        top: 0;
        left: 50%;
        margin-left: -121px;
      }

      .txt {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        position: absolute;
        top: 50%;
        margin-top: -21px;
        left: 50%;
        margin-left: -64px;
      }
    }

    .zjgs-box {
      display: flex;
      background: #e6e6e6;
      width: max-content;
      margin: 0 auto 40px;
      border-radius: 100px;

      div {
        padding: 12px 24px 11px;
        border-radius: 24px;
        font-size: 24px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #808080;
        border-radius: 100px;
        cursor: pointer;
      }

      .zjgs-box-active {
        background: #bf0614;
        color: #fff;
      }
    }

    .zjgs-b {
      display: flex;
      justify-content: space-between;

      .zjgs-b-l {
        width: 50%;
        height: 565px;
        position: relative;
        cursor: pointer;
        border-radius: 2px;
        overflow: hidden;
        box-shadow: 0px 0px 5px 1px rgba(162, 162, 162, 0.5);

        &:hover .zjgs-bj {
          display: block;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .zjgs-bj {
          display: none;
          background: rgba(0, 0, 0, 0.5);
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          color: #fff;
          padding: 130px 95px;
          transition: 0.5s;

          .zjgs-name {
            font-size: 32px;
            font-weight: 400;
            color: #ffffff;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;

            &::after {
              content: "";
              display: block;
              width: 60px;
              height: 4px;
              background: #ffffff;
              border-radius: 2px;
              margin-top: 29px;
            }
          }

          .zjgs-desc {
            font-size: 20px;
            font-weight: 400;
            color: #ffffff;
            line-height: 45px;
            margin-top: 20px;
            overflow: hidden; //超出的文本隐藏
            display: -webkit-box;
            -webkit-line-clamp: 4; // 超出多少行
            -webkit-box-orient: vertical;
          }
        }
      }

      .zjgs-b-r {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 49%;


        .zjgs-b-r-box {
          width: 49%;
          box-shadow: 0px 0px 5px 1px rgba(162, 162, 162, 0.5);
          background: #ffffff;
          height: max-content;
          margin-bottom: 18px;
          overflow: hidden;
          cursor: pointer;

          .img-top {
            width: 100%;
            height: 204px;
            overflow: hidden;

            img {
              display: block;
              width: 100%;
              height: 204px;
              object-fit: cover;
              transition: 0.5s;

              &:hover {
                transform: scale(1.1);
              }
            }
          }

          .zjgs-b-r-b {
            padding: 10px 20px;
            min-height: 69px;
            box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);

            .zjgs-b-r-name {
              font-size: 18px;
              font-weight: 400;
              color: #333333;
              margin-bottom: 5px;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
            }

            .zjgs-b-r-desc {
              font-size: 15px;
              font-weight: 400;
              color: #808080;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
            }
          }
        }
      }
    }

    .zjgs-item {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      a {
        color: #ffffff;
      }

      .zjgs-list {
        -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
        -moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
        width: 460px;
        height: 220px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-bottom: 20px;
        background-image: url("../../assets/首页半圆.png");

        &:nth-child(1) {
          background-color: #3fabeb;
        }

        &:nth-child(2) {
          background-color: #dc808b;
        }

        &:nth-child(3) {
          background-color: #81a4da;
        }

        &:nth-child(4) {
          background-color: #51cea6;
        }

        &:nth-child(5) {
          background-color: #dcab68;
        }

        &:nth-child(6) {
          background-color: #dd8281;
        }

        .zjgs-title {
          font-size: 37px;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          flex-direction: column;
          align-items: center;

          &::after {
            content: "";
            display: block;
            width: 30px;
            height: 3px;
            background: #ffffff;
            border-radius: 2px;
            margin-top: 10px;
          }
        }

        ul {
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          max-width: 320px;
          flex-wrap: wrap;
          justify-content: center;
          line-height: 30px;
          margin-top: 10px;

          li {
            cursor: pointer;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1480px) {
  .content .banner .banner-item {
    position: relative;
  }

  .content .zjgs {
    padding: 0 30px;
  }

  nav .nav-item {
    padding: 0 25px;
  }

  .content .szyw {
    // padding: 0 30px;
  }

  .content .btwh {
    // padding: 0 30px;
  }

  .content .btwh .btwh-item {
    margin-top: 40px;
  }

  .content .zjgs .zjgs-item {
    justify-content: space-evenly;
  }
}

@media screen and (max-width: 1200px) {
  nav .nav-item .nav-right .nav-title ul li {
    margin-right: 38px;
  }
}

@media screen and (max-width: 1130px) {
  nav .nav-item .nav-right .nav-title ul li {
    margin-right: 38px;
  }
}

@media screen and (max-width: 1120px) {

  // .nav .nav-item {
  //   // max-width: 1420px;
  // }
  nav .nav-item .logo img {
    height: 56px;
    margin-top: 12px;
  }

  .content .banner .banner-item .banner-item-news .news-title {
    height: auto;
    padding: 0 60px 10px;
  }

  nav {
    height: 45px;
  }

  nav .nav-box {
    height: 70px;
  }

  .content .banner-box {
    padding: 0 30px;
  }

  .content .banner .banner-item .up-left {
    top: -20px;
  }

  .content .banner .banner-item .swiper1 {
    width: 100%;
  }

  .content .btwh .service {
    width: 100%;
  }

  // .content .btwh .service .service-item .el-button--text {
  //   margin-right: 10px;
  //   &:nth-child(7n) {
  //     margin-right: 0;
  //   }
  // }
  // .content .btwh .service .service-item {
  //   justify-content: flex-start;
  // }
  nav .nav-item .self {

    display: block;
  }

  .content .btwh .btwh-img {
    margin: 41px auto 19px;
  }

  .content .btwh .btwh-down .sfq {
    display: none;
  }

  .content .btwh .btwh-down .sfq1 {
    display: block;
  }

  nav .nav-item {
    padding: 0 30px;
    height: 70px;

    .el-icon-close {
      // display: block;
      font-size: 30px;
    }

    .el-icon-more {
      display: block;
      font-size: 30px;
    }
  }

  nav .nav-item .nav-right {
    display: none;
  }

  .content .szyw .szyw-item {
    flex-wrap: wrap;
    padding: 0 30px;
  }

  .content .banner {
    height: 1250px;
    background: #f7f7f7;
  }

  .content .banner .banner-item {
    display: block;
  }

  .content .banner .banner-item .banner-item-news {
    background: #f7f7f7;
    width: 100%;
  }

  .content .content-item {
    margin: 0 auto;
  }

  .content .szyw .szyw-img {
    margin: 57px auto 19px;
  }

  // .content .bj {
  //   max-width: 1420px;
  //   height: auto;
  // }
  .content .szyw .dwdt {
    margin: 20px 30px;
  }

  .content .szyw .dwdt ul li {
    &:nth-child(3n) {
      margin-right: 70px;
    }

    &:nth-child(2n) {
      margin-right: 0;
    }
  }

  .content .btwh .btwh-down {
    flex-wrap: wrap;
    padding: 0 30px;
  }

  .content .btwh .btwh-item {
    padding: 0 30px;
  }

  .content .banner .bj-box {
    display: none;
  }

  .content .zjgs .zjgs-item {
    padding: 0 30px;
  }
}

@media screen and (max-width: 1090px) {
  // .content .btwh .service .service-item .el-button--text {
  //   margin-right: 28px;
  //   &:nth-child(6n) {
  //     margin-right: 0;
  //   }
  //   &:nth-child(7n) {
  //     margin-right: 28px;
  //   }
  // }
}

@media screen and (max-width: 1038px) {
  // .content .btwh .service .service-item .el-button--text {
  //   margin-right: 15px;
  //   &:nth-child(6n) {
  //     margin-right: 0;
  //   }
  //   &:nth-child(7n) {
  //     margin-right: 15px;
  //   }
  // }
}

@media screen and (max-width: 975px) {
  // .content .btwh .service .service-item .el-button--text {
  //   margin-right: 40px;
  //   &:nth-child(5n) {
  //     margin-right: 0;
  //   }
  //   &:nth-child(6n) {
  //     margin-right: 40px;
  //   }
  //   &:nth-child(7n) {
  //     margin-right: 40px;
  //   }
  // }
}

@media screen and (max-width: 925px) {
  // .content .btwh .service .service-item .el-button--text {
  //   margin-right: 25px;
  //   &:nth-child(5n) {
  //     margin-right: 0;
  //   }
  //   &:nth-child(6n) {
  //     margin-right: 25px;
  //   }
  //   &:nth-child(7n) {
  //     margin-right: 25px;
  //   }
  // }
}

@media screen and (max-width: 862px) {
  // .content .btwh .service .service-item .el-button--text {
  //   margin-right: 10px;
  //   &:nth-child(5n) {
  //     margin-right: 0;
  //   }
  //   &:nth-child(6n) {
  //     margin-right: 10px;
  //   }
  //   &:nth-child(7n) {
  //     margin-right: 10px;
  //   }
  // }
}

@media screen and (max-width: 805px) {
  // .content .btwh .service .service-item .el-button--text {
  //   margin-right: 40px;
  //   &:nth-child(4n) {
  //     margin-right: 0;
  //   }
  //   &:nth-child(5n) {
  //     margin-right: 40px;
  //   }
  //   &:nth-child(6n) {
  //     margin-right: 40px;
  //   }
  //   &:nth-child(7n) {
  //     margin-right: 40px;
  //   }
  // }
}

.tou {
  display: flex;
  align-self: center;
  align-items: center;
  justify-content: space-between;
  padding: 20px 230px 30px 230px
}

.logo img {
  height: 80px;
}

@media screen and (max-width: 750px) {
  .news-box{
    width: 100%;
    margin-top: 30px;
    height: auto;
  }
  .content .banner .banner-item{
    padding: 0px 30px 0px 30px;
  }
  .tou {
    display: flex;
    align-self: center;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 0px 30px
  }

  .logo img {
    height: 50px;
  }

  .content .btwh .btwh-down .sfq1 .sfq1-top .more-title1 {
    display: block;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
    cursor: pointer;
  }

  .content .banner-box {
    padding: 0;
  }

  .content .btwh .btwh-down .sfq1 .sfq1-top .sfq1-top-more {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .content .banner {
    height: max-content;
    padding-bottom: 20px;
    box-shadow: none;
  }

  .content .news {
    padding: 0 30px;
    margin-top: 25px;
  }

  .content .banner .banner-bottom .swiper5 {
    display: block;
  }

  .content .banner .banner-bottom .swiper2 {
    display: none;
  }

  .content .zjgs .zjgs-box div {
    font-size: 16px;
    padding: 5px 13px 5px;
    margin-bottom: 10px;
  }

  .content .btwh .service .service-item .el-button--text {
    font-size: 16px;
    width: 33% !important;
  }

  .content .zjgs .zjgs-b .zjgs-b-r .zjgs-b-r-box .zjgs-b-r-b .zjgs-b-r-name {
    font-size: 16px;
  }

  .content .zjgs .zjgs-b .zjgs-b-r .zjgs-b-r-box .zjgs-b-r-b .zjgs-b-r-desc {
    font-size: 14px;
  }

  .content .news .news-title {
    font-size: 22px;
  }

  .content .news .news-list {
    margin: 20px 0 30px;
  }

  .content .banner .banner-bottom {
    position: static;
    margin-top: 40px;
  }

  .content .banner .banner-item .up-left {
    display: none;
  }

  .content .banner .banner-item {
    top: 0;
  }

  .content .banner .banner-item .banner-item-news {
    height: auto;
    margin-bottom: 20px;
    display: none;
  }

  .content .banner .banner-item #banner-item-news {
    display: block;
  }

  .content .banner .banner-item .swiper1 {
    width: 100%;
    height: max-content;
  }

  .content .banner .banner-item .swiper1 .swiper-slide {
    height: auto;
  }

  .content .btwh .btwh-img {
    margin: 5px auto -76px;
  }

  .content .zjgs .zjgs-img {
    margin: 5px auto 0px;
    height: 80px;
  }

  .content .zjgs .zjgs-img .bj {
    font-size: 55px;
    text-align: center;
  }

  .content .zjgs .zjgs-img .txt {
    font-size: 22px;
    margin-left: -41px;
    margin-top: -17px;
  }

  .content .szyw .szyw-img .bj {
    font-size: 55px;
  }

  .content .szyw .szyw-img .txt {
    font-size: 22px;
    margin-top: -29px;
    margin-left: -48px;
  }

  .content .btwh .btwh-img .bj {
    font-size: 55px;
    text-align: center;
  }

  .content .banner .banner-item .banner-item-news ul {
    padding: 0;
  }

  .content .btwh .btwh-img .txt {
    font-size: 22px;
    margin-left: -37px;
    margin-top: -28px;
  }

  .content .btwh .btwh-img {
    text-align: center;
  }

  .content .banner .banner-bottom .banner-icon2 {
    display: none;
  }

  .content .banner .banner-bottom .banner-icon1 {
    display: none;
  }

  .content .btwh .btwh-down .sfq1 .sfq1-top .top-item div {
    margin: 5px 10px 5px 0;
    padding: 2px 10px 2px;
    font-size: 18px;
    flex-shrink: 0;
  }

  .content .btwh .service {
    box-shadow: none;
  }

  .content .btwh .btwh-down .sfq1 .sfq1-actice {
    color: #fff !important;
    background: #bf0614;
    border-radius: 100px;
  }

  .content .btwh .service .service-title {
    font-size: 20px;
    height: 35px;
  }

  // .content .btwh .service .service-item .el-button--text {
  //   margin-right: 9px !important;
  // }
  .content .szyw {
    padding: 0;
  }

  .content .szyw .szyw-img {
    margin: 9px auto -35px;
  }

  .content .btwh .btwh-item {
    padding: 0 30px;
  }

  .content .banner .banner-item .banner-item-news .news-title {
    height: auto;
    line-height: 33px;
    font-size: 22px;
    padding: 0 0 5px;
  }

  .content .banner .banner-item .banner-item-news ul li span {
    font-size: 16px;
  }

  .content .banner .banner-item .banner-item-news ul li {
    line-height: 35px;
  }

  .content .btwh .btwh-down {
    padding: 0 30px;
  }

  .content .btwh .btwh-down .sfq1 .sfq1-top {
    display: block;
    overflow: hidden;
  }

  .content .btwh .btwh-down .sfq1 .sfq1-top .top-item {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 10px;
    overflow-x: scroll;
    padding-bottom: 10px;
  }

  .content .btwh .btwh-down .sfq1 {
    height: max-content;
    box-shadow: none;
    padding: 20px 20px 10px;
  }

  .content .btwh .btwh-down .sfq1 .sfq1-top .sfq-title {
    font-size: 20px;
  }

  .content .btwh {
    overflow-x: hidden;
  }

  // .content .news .news-list .gang {
  //   display: none;
  // }
  // .content .news .news-list {
  //   display: block;
  // }
  // .content .news .news-list .news-list-name {
  //   display: block;
  //   max-width: 100%;
  // }
  .content .zjgs .zjgs-box {
    display: flex;
    border-radius: 25px;
    background: none;
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 auto 10px;
    border-radius: 0;
  }

  .content .zjgs .zjgs-b {
    display: block;
  }

  .content .zjgs .zjgs-b .zjgs-b-l {
    width: 100%;
    margin-bottom: 30px;
    display: none;
  }

  .content .zjgs .zjgs-b .zjgs-b-r {
    width: 100%;
    // padding: 0 30px;
  }

  .content .btwh .btwh-down .sfq1 .sfq-item ul {
    font-size: 16px;
    padding-left: 0;
  }

  .content .btwh .btwh-down .sfq1 .sfq-item {
    margin-top: 0;
  }

  .content .btwh .btwh-down .sfq1 .sfq-item ul li {
    line-height: 35px;
  }

  .content .btwh .btwh-down .sfq1 .sfq1-top .top-item .more-title {
    display: none;
  }
}

@media screen and (max-width: 530px) {
  .content .zjgs .zjgs-box {
    width: auto;
    flex-wrap: nowrap;
    overflow-x: scroll;
    justify-content: flex-start;
    scrollbar-width: none;
    /* firefox */
    padding-bottom: 5px;
  }

  .content .zjgs .zjgs-box-item {
    width: 100%;
    overflow: hidden;
  }

  .content .zjgs .zjgs-box div {
    flex-shrink: 0;
    margin: 0 3px 10px;
  }
}

@media screen and (max-width: 500px) {
  .content .cbl .cbl-b {
    font-size: 16px;
    width: 120px;
    height: 33px;
  }

  .content .zjgs .zjgs-b .zjgs-b-r .zjgs-b-r-box {
    width: 100%;
  }

  .content .zjgs .zjgs-img {
    height: 69px;
  }

  .content .zjgs .zjgs-img .bj {
    font-size: 45px;
  }

  .content .btwh .service .service-item .el-button--text {
    width: 105px;
  }

  .content .banner {
    padding-bottom: 0;
  }

  .content .szyw .szyw-img {
    height: 80px;
  }

  .content .szyw .szyw-img .bj {
    font-size: 45px;
    left: 0;
    margin-left: 0;
    width: 100%;
  }

  .content .szyw .szyw-img .txt {
    font-size: 22px;
    margin-top: -22px;
    margin-left: -37px;
  }

  .content .btwh .btwh-img {
    height: 75px;
    margin-bottom: -68px;
  }

  .content .btwh .btwh-img .bj {
    font-size: 45px;
  }

  .content .btwh .btwh-img .txt {
    font-size: 22px;
    margin-left: -30px;
    margin-top: -21px;
  }
}

@media screen and (max-width: 430px) {
  .content .banner .banner-bottom .yidong1 {
    display: block;
    position: absolute;
    color: #fff;
    z-index: 2;
    left: 40px;
    font-size: 40px;
  }

  .content .banner .banner-bottom .yidong2 {
    display: block;
    position: absolute;
    right: 40px;
    color: #fff;
    z-index: 2;
    font-size: 40px;
  }

  .content .banner .banner-bottom .swiper5 .swiper-slide img {
    width: 100%;
    height: 100%;
  }

  .content .banner .banner-bottom .swiper5 .swiper-slide-active {
    transform: scale(1) !important;
  }

  .content .banner .banner-bottom .swiper5 .swiper-slide {
    width: 100%;
    height: 46vw;
  }
}

@media screen and (max-width: 415px) {
  .content .btwh .service .service-item .el-button--text {
    width: 130px;
  }
}

@media screen and (max-width: 415px) {
  .content .btwh .service .service-item .el-button--text {
    width: 100%;
  }
}

@media screen and (max-width: 320px) {
  // .content .btwh .service .service-item .el-button--text {
  //   &:nth-child(2n) {
  //     margin-right: 0 !important;
  //   }
  // }
}
</style>
<style>
.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
  background: #979fb0;
  opacity: 1;
}

[v-cloak] {
  display: none;
}
</style>
