<template>
  <div class="localcuisine">
    <Header />
    <div class="banner">
      <img src="../../assets/01.jpg" alt="" />
    </div>
    <div class="item">
      <div class="item-box">
        <div class="item-l">
          <div class="mlgs">
            <div></div>
            <div class="title">魅力固始</div>
            <div></div>
          </div>
          <ul>
            <li>
              <router-link :to="{ name: 'localcuisine' }">本地美食</router-link>
            </li>
            <li>
              <router-link :to="{ name: 'gushibeauty' }">固始风景</router-link>
            </li>
            <li>绿色农产品</li>
            <li>
              <router-link :to="{ path: `/news/${39}/${18}` }"
                >非遗文化</router-link
              >
            </li>
            <li>本地医院</li>
            <li>商超</li>
            <li>城市风光</li>
            <li>招商引资</li>
            <li>人才招聘</li>
            <li>名人轶事</li>
          </ul>
        </div>
        <div class="item-r">
          <el-breadcrumb class="crumbs" separator-class="el-icon-arrow-right">
            <el-breadcrumb-item
              ><router-link
                style="color: #606266; font-weight: initial"
                :to="{ path: '/' }"
                >首页</router-link
              >
            </el-breadcrumb-item>
            <el-breadcrumb-item>本地美食</el-breadcrumb-item>
          </el-breadcrumb>
          <div class="lists" v-loading="loading">
            <div
              class="finefood"
              v-for="item in meishiList.slice(
                (currentPage - 1) * pageSize,
                currentPage * pageSize
              )"
              :key="item.id"
              @click="goDetail(item.id)"
            >
              <img :src="item.news_image[0]" alt="" />
              <div class="finefood-item">
                <div class="title">{{ item.news_title }}</div>
                <div class="desc">{{ item.news_titleshort }}</div>
                <div class="viewdetails">
                  <div class="btn">查看详情</div>
                </div>
              </div>
            </div>
            <!-- <div class="finefood">
              <img src="../../assets/finefood.png" alt="" />
              <div class="finefood-item">
                <div class="title">固始鹅块</div>
                <div class="desc">
                  固始汗鹅块是以固始鹅为原料，经过特殊的烹饪过程加工而成的一道特色地方菜。做固始汗鹅块时，需先把洗净的整个鹅加葱、姜清炖煮熟，肉汤分离备用。然后用鹅油炝锅固始汗鹅块是以固始鹅为原料，
                </div>
                <div class="viewdetails">
                  <div class="btn">查看详情</div>
                </div>
              </div>
            </div>
            <div class="finefood">
              <img src="../../assets/finefood.png" alt="" />
              <div class="finefood-item">
                <div class="title">固始鹅块</div>
                <div class="desc">
                  固始汗鹅块是以固始鹅为原料，经过特殊的烹饪过程加工而成的一道特色地方菜。做固始汗鹅块时，需先把洗净的整个鹅加葱、姜清炖煮熟，肉汤分离备用。然后用鹅油炝锅固始汗鹅块是以固始鹅为原料，
                </div>
                <div class="viewdetails">
                  <div class="btn">查看详情</div>
                </div>
              </div>
            </div>
            <div class="finefood">
              <img src="../../assets/finefood.png" alt="" />
              <div class="finefood-item">
                <div class="title">固始鹅块</div>
                <div class="desc">
                  固始汗鹅块是以固始鹅为原料，经过特殊的烹饪过程加工而成的一道特色地方菜。做固始汗鹅块时，需先把洗净的整个鹅加葱、姜清炖煮熟，肉汤分离备用。然后用鹅油炝锅固始汗鹅块是以固始鹅为原料，
                </div>
                <div class="viewdetails">
                  <div class="btn">查看详情</div>
                </div>
              </div>
            </div>
            <div class="finefood">
              <img src="../../assets/finefood.png" alt="" />
              <div class="finefood-item">
                <div class="title">固始鹅块</div>
                <div class="desc">
                  固始汗鹅块是以固始鹅为原料，经过特殊的烹饪过程加工而成的一道特色地方菜。做固始汗鹅块时，需先把洗净的整个鹅加葱、姜清炖煮熟，肉汤分离备用。然后用鹅油炝锅固始汗鹅块是以固始鹅为原料，
                </div>
                <div class="viewdetails">
                  <div class="btn">查看详情</div>
                </div>
              </div>
            </div>
            <div class="finefood">
              <img src="../../assets/finefood.png" alt="" />
              <div class="finefood-item">
                <div class="title">固始鹅块</div>
                <div class="desc">
                  固始汗鹅块是以固始鹅为原料，经过特殊的烹饪过程加工而成的一道特色地方菜。做固始汗鹅块时，需先把洗净的整个鹅加葱、姜清炖煮熟，肉汤分离备用。然后用鹅油炝锅固始汗鹅块是以固始鹅为原料，
                </div>
                <div class="viewdetails">
                  <div class="btn">查看详情</div>
                </div>
              </div>
            </div> -->
          </div>
          <div class="fenye">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="total"
              :current-page="currentPage"
              :page-size="pageSize"
              @current-change="current_change"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- <recommend /> -->
    <Footers />
  </div>
</template>
<script>
import Header from "@/components/header/header.vue";
import Footers from "@/components/footers/footers.vue";
import newsImgList from "@/components/newsImgList/newsImgList.vue";
import recommend from "@/components/recommend/recommend.vue";
import { currentPolitics } from "@/api/api.js";
export default {
  name: "localcuisine",
  data() {
    return {
      meishiList: [],
      total: 1,
      currentPage: 1,
      page: 1,
      pageSize: 6,
      loading: false,
    };
  },
  mounted() {
    this.getMeishi();
  },
  computed: {},
  methods: {
    getMeishi() {
      this.loading = true;
      currentPolitics(8).then((res) => {
        // console.log(res);
        this.meishiList = res.data.data.news;
        this.total = this.meishiList.length;
        this.currentPage = 1;
        this.loading = false;
      });
    },
    current_change(currentPage) {
      //改变当前页
      this.currentPage = currentPage;
      window.scrollTo(0, 0);
    },
    goDetail(id) {
      this.$router.push({
        path: `/gourmetdetails/${id}`,
      });
    },
  },
  components: {
    Header,
    Footers,
    newsImgList,
    recommend,
  },
};
</script>
<style scoped lang="scss">
.banner {
  // height: 360px;
  width: 100%;
  img {
    // height: 360px;
    width: 100%;
    min-height: 140px;
    object-fit: cover;
    display: block;
  }
}
.item {
  .item-box {
    max-width: 1420px;
    margin: 0 auto;
    display: flex;
  }
  .item-l {
    width: 330px;
    transform: translateY(-80px);
    margin-right: 30px;
    .mlgs {
      width: 330px;
      height: 240px;
      background: linear-gradient(0deg, #bf0614, #ff4c4c);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30px 0;
      justify-content: space-between;
      div {
        font-size: 32px;
        font-weight: 400;
        color: #ffffff;
        &:nth-child(1) {
          width: 120px;
          height: 40px;
          border: 1px solid #ffffff;
          border-bottom: none;
        }
        &:nth-child(3) {
          width: 120px;
          height: 40px;
          border: 1px solid #ffffff;
          border-top: none;
        }
      }
    }
    ul {
      li {
        width: 330px;
        height: 80px;
        font-size: 24px;
        font-weight: 400;
        color: #333333;
        line-height: 80px;
        text-align: center;
        a {
          font-size: 24px;
          font-weight: 400;
          color: #333333;
        }
        &:nth-child(odd) {
          background: #ffffff;
        }
        &:nth-child(even) {
          background: #f7f7f7;
        }
        &:hover {
          background: linear-gradient(0deg, #bf0614, #ff4c4c);
          color: #ffffff;
        }
        &:hover a {
          color: #ffffff;
        }
        cursor: pointer;
      }
    }
  }
  .item-r {
    width: 1060px;
    .crumbs {
      font-size: 16px;
      font-weight: 400;
      color: #808080;
      height: 50px;
      background: #f7f7f7;
      line-height: 50px;
      padding-left: 16px;
      border-bottom: 1px solid #cccccc;
    }
    .lists {
      .finefood {
        display: flex;
        border-bottom: 1px dashed #808080;
        padding: 30px 0;
        cursor: pointer;
        &:hover .finefood-item .btn {
          background: #bf0614;
        }
        img {
          width: 340px;
          height: 204px;
          border-radius: 8px;
          margin-right: 30px;
        }
        .finefood-item {
          height: 204px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          flex: 1;
          .title {
            font-size: 24px;
            font-weight: 400;
            color: #333333;
          }
          .desc {
            font-size: 20px;
            font-weight: 400;
            color: #808080;
            line-height: 40px;
            margin-top: 10px;
            overflow: hidden; //超出的文本隐藏
            display: -webkit-box;
            -webkit-line-clamp: 3; // 超出多少行
            -webkit-box-orient: vertical;
          }
          .btns {
            text-align: right;
          }
          .viewdetails {
            display: flex;
            justify-content: flex-end;
          }
          .btn {
            width: 120px;
            height: 40px;
            background: #cccccc;
            border-radius: 20px;
            font-size: 20px;
            font-weight: 400;
            color: #ffffff;
            line-height: 40px;
            text-align: center;
          }
        }
      }
    }
    .fenye {
      margin: 41px 0 60px;
      text-align: center;
    }
  }
}
.jctj {
  background: #f7f7f7;
  padding-top: 57px;
  padding-bottom: 132px;
  .jctj-item {
    width: 1420px;
    margin: 0 auto;
    .jctj-img {
      height: 63px;
      display: block;
      margin: 0 auto 59px;
    }
    .culture {
      display: flex;
      justify-content: space-between;
    }
  }
}
@media screen and (max-width: 1120px) {
  .item .item-l {
    display: none;
  }
  .item .item-r {
    margin: 0 auto;
  }
}

@media screen and (max-width: 750px) {
  .item .item-r .fenye {
    margin: 20px 0 25px;
  }
  .item .item-l {
    display: none;
  }
  .item .item-r {
    margin: 0 auto;
  }
  .item .item-r .lists .finefood {
    padding: 15px 10px;
    display: flex;
  }
  .item .item-r .lists .finefood img {
    width: 138px;
    height: 92px;
    margin-right: 10px;
  }
  .item .item-r .lists .finefood .finefood-item .viewdetails {
    display: none;
  }
  .item .item-r .lists .finefood .finefood-item {
    margin-top: 0;
  }
  .item .item-r .lists .finefood .finefood-item .title {
    font-size: 20px;
    width: 100%;
    overflow: hidden; //超出的文本隐藏
    display: -webkit-box;
    -webkit-line-clamp: 1; // 超出多少行
    -webkit-box-orient: vertical;
  }
  .item .item-r .lists .finefood .finefood-item .desc {
    font-size: 16px;
    line-height: 1.5;
    -webkit-line-clamp: 2;
  }
  .item .item-r .lists .finefood .finefood-item {
    height: auto;
  }
}
</style>
<style>
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background: linear-gradient(0deg, #bf0614, #ff4c4c);
}
</style>
