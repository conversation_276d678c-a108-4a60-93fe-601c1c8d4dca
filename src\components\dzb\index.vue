<template>
    <div class="news-box">
        <div class="news-box-top">
            <div>今日固始</div>
        </div>
        <div class="fen">
            <div @click="goDetail(newsImgList[0].url)" style="cursor: pointer;width: 50%;">
                <img style="width: 100%;height: 100%;" :src="`https://admin.gsw.gov.cn` + newsImgList[0].image">
            </div>
            <div @click="goDetail(newsImgList[1].url)" style="cursor: pointer;width: 50%;">
                <img style="width: 100%;height: 100%;" :src="`https://admin.gsw.gov.cn` + newsImgList[1].image">
            </div>
        </div>
        <div class="fen_x">
            <div @click="goDetail(newsImgList[0].url)"
                style="font-size: 20px;text-align: center;color: #cc3232;cursor: pointer;">
                <div>{{ newsImgList[0].title }}</div>
                <div style="font-size: 15px;margin-top: 5px;">{{ newsImgList[0].date }}</div>
            </div>
            <div style="color: #a6a6a8;">
                |
            </div>
            <div @click="goDetail(newsImgList[1].url)"
                style="font-size: 20px;text-align: center;color: #cc3232;cursor: pointer;">
                <div>{{ newsImgList[1].title }}</div>
                <div style="font-size: 15px;margin-top: 5px;">{{ newsImgList[1].date }}</div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "dzbList",
    props: {
        newsImgList: {
            type: Array,
            default: [],
        },
    },
    data() {
        return {};
    },
    mounted() {
        console.log(this.newsImgList);
    },
    computed: {},
    methods: {
        goDetail(id) {
            window.open(id);
        },
    },
};
</script>
<style scoped lang="scss">
.news-box {
    width: 32%;
    height: 500px;
    padding: 20px;
    background: #ffffff;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
    .news-box-top {
        width: 100%;
        display: flex;
        height: 40px;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #bfbeba;

        div {
            &:nth-child(1) {
                font-size: 24px;
                font-weight: 400;
                color: #bf0614;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                &::after {
                    content: "";
                    display: block;
                    background: #bf0614;
                    height: 3px;
                    width: 110px;
                    margin-top: 6px;
                }
            }

            &:nth-child(2) {
                font-size: 16px;
                font-weight: 400;
                color: #808080;
                cursor: pointer;

                &:hover {
                    color: #bf0614;
                }
            }
        }
    }
}

.fen {
    display: flex;
    justify-content: center;
    background-color: rgb(255, 255, 255);
}

.fen_x {
    display: flex;
    justify-content: space-around;
    background-color: #e8e8ea;
    align-items: center;
    padding: 10px 0;
    height: 120px;
}
</style>