<template>
  <div>
    <Header />
    <div class="banner">
      <img src="../../assets/01.jpg" alt="" />
    </div>
    <div class="crumbs-item">
      <el-breadcrumb class="crumbs" separator-class="el-icon-arrow-right">
        <el-breadcrumb-item
          ><router-link
            style="color: #606266; font-weight: initial"
            :to="{ path: '/' }"
            >首页</router-link
          >
        </el-breadcrumb-item>
        <el-breadcrumb-item>固始风景</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="main">
      <div class="main-item">
        <div class="main-top">
          <div class="main-swiper">
            <div class="swiper-container swiper1">
              <div class="swiper-wrapper">
                <div
                  class="swiper-slide"
                  v-for="(item, index) in con.news_image"
                  :key="index"
                >
                  <img :src="item" alt="" />
                </div>
                <!-- <div class="swiper-slide">
                  <img src="../../assets/tu7.jpg" alt="" />
                </div>
                <div class="swiper-slide">
                  <img src="../../assets/tu7.jpg" alt="" />
                </div>
                <div class="swiper-slide">
                  <img src="../../assets/tu7.jpg" alt="" />
                </div> -->
              </div>
              <!-- 如果需要分页器 -->
              <div class="swiper-pagination"></div>
            </div>
          </div>
          <div class="desc">
            <div class="title">
              {{ con.news_title }} <span>{{ con.level }}A</span>
            </div>
            <div class="introduction">
              <span>地址</span>
              <span>{{ con.news_title }}</span>
            </div>
            <div class="introduction">
              <span>开放时间</span>
              <span>{{ con.opening_hours }}</span>
            </div>
            <div class="introduction">
              <span>官方电话</span>
              <span>{{ con.phone }}</span>
            </div>
            <!-- <div class="vr">VR畅游</div> -->
          </div>
        </div>
        <div class="main-bottom">
          <div class="scenic">
            <div class="title">景区介绍</div>
            <div class="desc" v-html="con.news_content"></div>
            <div v-if="con.policy" class="title">优待政策</div>
            <div v-if="con.policy" class="desc" v-html="con.policy"></div>
            <div v-if="con.facility" class="title">服务设施</div>
            <div v-if="con.facility" class="desc" v-html="con.facility"></div>
          </div>
          <div class="nearby">
            <div class="title-top">
              <div
                :class="nearbyIndex == index ? 'title-active' : 'title'"
                v-for="(item, index) in nearbyList"
                :key="index"
                @click="isNearby(index, item.if_type)"
              >
                {{ item.name }}
              </div>
            </div>
            <div class="list">
              <div class="list-item" v-for="item in hotelList" :key="item.id">
                <img :src="item.images[0]" alt="" />
                <div class="list-desc">
                  <div class="name">{{ item.name }}</div>
                  <div class="location">{{ item.address }}</div>
                  <el-rate
                    v-model="item.level"
                    disabled
                    disabled-void-color="#CCCCCC"
                    :colors="['#BF0614', '#BF0614', '#BF0614']"
                  >
                  </el-rate>
                  <!-- <div class="distance">距离23.9km</div> -->
                </div>
              </div>
              <!-- <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="list-desc">
                  <div class="name">国际根亲文化酒店</div>
                  <div class="location">陈淋子镇九华山茶场院内</div>
                  <div class="distance">距离23.9km</div>
                </div>
              </div>
              <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="list-desc">
                  <div class="name">国际根亲文化酒店</div>
                  <div class="location">陈淋子镇九华山茶场院内</div>
                  <div class="distance">距离23.9km</div>
                </div>
              </div>
              <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="list-desc">
                  <div class="name">国际根亲文化酒店</div>
                  <div class="location">陈淋子镇九华山茶场院内</div>
                  <div class="distance">距离23.9km</div>
                </div>
              </div>
              <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="list-desc">
                  <div class="name">国际根亲文化酒店</div>
                  <div class="location">陈淋子镇九华山茶场院内</div>
                  <div class="distance">距离23.9km</div>
                </div>
              </div>
              <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="list-desc">
                  <div class="name">国际根亲文化酒店</div>
                  <div class="location">陈淋子镇九华山茶场院内</div>
                  <div class="distance">距离23.9km</div>
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <recommend />
    <Footers />
  </div>
</template>
<script>
import Header from "@/components/header/header.vue";
import Footers from "@/components/footers/footers.vue";
import newsImgList from "@/components/newsImgList/newsImgList.vue";
import recommend from "@/components/recommend/recommend.vue";
import Swiper from "swiper";
import { details, hotel } from "@/api/api.js";
export default {
  name: "scenicdetails",
  data() {
    return {
      nearbyList: [
        {
          id: 1,
          if_type: 1,
          name: "附近住宿",
        },
        {
          id: 2,
          if_type: 2,
          name: "附近美食",
        },
      ],
      nearbyIndex: 0,
      con: {},
      hotelList: [],
    };
  },
  mounted() {
    this.getDetail();
    this.getHotel(1);
  },
  computed: {},
  methods: {
    initSwiper1() {
      new Swiper(".swiper1", {
        loop: true, // 循环模式选项
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        // 如果需要分页器
        pagination: {
          el: ".swiper-pagination",
        },
      });
    },
    isNearby(index, if_type) {
      this.nearbyIndex = index;
      this.getHotel(if_type);
    },
    getDetail() {
      let id = this.$route.params.id;
      details(id).then((res) => {
        // console.log(res);
        this.con = res.data.data;
        this.$nextTick(() => {
          this.initSwiper1();
        });
      });
    },
    getHotel(if_type) {
      hotel(if_type).then((res) => {
        // console.log(res);
        this.hotelList = res.data.data.slice(0, 6);
      });
    },
  },
  components: {
    Header,
    Footers,
    newsImgList,
    recommend,
  },
};
</script>
<style>
.swiper-container-horizontal
  > .swiper-pagination-bullets
  .swiper-pagination-bullet {
  background: #979fb0;
  opacity: 1;
}
</style>
<style scoped lang="scss">
.banner {
  // height: 360px;
  width: 100%;
  img {
    // height: 360px;
    width: 100%;
    min-height: 140px;
    object-fit: cover;
    display: block;
  }
}
.crumbs-item {
  background: #f7f7f7;
  border-bottom: 1px solid #cccccc;
  .crumbs {
    height: 50px;
    max-width: 1420px;
    margin: 0 auto;
    line-height: 50px;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
  }
}
.main {
  padding: 40px 0 60px;
  .main-item {
    max-width: 1420px;
    margin: 0 auto;
  }
  .main-top {
    background: #f7f7f7;
    padding: 30px;
    display: flex;
    .main-swiper {
      width: 67%;
      margin-right: 30px;
      .swiper1 {
        width: 100%;
        height: 504px;
        ::v-deep .swiper-pagination-bullet-active {
          background-color: #fff;
        }
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    .desc {
      flex: 1;
      .title {
        font-size: 32px;
        font-weight: 400;
        color: #333333;
        position: relative;
        margin-top: 10px;
        margin-bottom: 25px;
        display: flex;
        align-items: flex-start;
        span {
          width: 32px;
          height: 20px;
          background: linear-gradient(-90deg, #bf0614, #ff4c4c);
          border-radius: 4px;
          display: inline-block;
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          text-align: center;
          line-height: 20px;
          margin: 8px 0px 0px 10px;
        }
      }
      .introduction {
        font-size: 20px;
        font-weight: 400;
        color: #808080;
        line-height: 40px;
        display: flex;
        span {
          display: inline-block;
          &:nth-child(1) {
            width: 90px;
            margin-right: 10px;
          }
        }
      }
      .vr {
        font-size: 20px;
        font-weight: 400;
        color: #ffffff;
        width: 150px;
        height: 40px;
        background: linear-gradient(-90deg, #46a1bc, #46cc9a);
        border-radius: 20px;
        line-height: 40px;
        text-align: center;
        margin-top: 35px;
        cursor: pointer;
      }
    }
  }
  .main-bottom {
    display: flex;
    margin-top: 20px;
    justify-content: space-between;
    .scenic {
      background: #f7f7f7;
      padding: 30px 30px 80px 30px;
      width: 1060px;
      .title {
        display: flex;
        font-size: 32px;
        font-weight: 400;
        align-items: center;
        color: #333333;
        &::before {
          content: "";
          display: block;
          width: 11px;
          height: 37px;
          background: linear-gradient(-90deg, #bf0614, #ff4c4c);
          margin-right: 9px;
        }
      }
      .desc {
        font-size: 20px;
        font-weight: 400;
        color: #333333;
        line-height: 45px;
        margin: 30px 0 50px;
        text-indent: 2em;
        text-align: justify;
        text-justify: distribute;
      }
    }
    .nearby {
      width: 340px;
      height: max-content;
      background: #f7f7f7;
      padding: 30px;
      .title-top {
        display: flex;
        align-items: center;
        .title-active {
          font-size: 24px;
          font-weight: 400;
          color: #333333;
          cursor: pointer;
          margin-right: 20px;
          &::after {
            content: "";
            display: block;
            width: 98px;
            height: 2px;
            background: linear-gradient(-90deg, #bf0614, #ff4c4c);
            border-radius: 1px;
          }
        }
        .title {
          font-size: 20px;
          font-weight: 400;
          color: #808080;
          margin-right: 20px;
          cursor: pointer;
        }
      }
      .list {
        .list-item {
          margin-top: 20px;
          display: flex;
          img {
            width: 90px;
            height: 72px;
            display: block;
            margin-right: 10px;
          }
          .list-desc {
            .name {
              font-size: 20px;
              color: #333333;
              width: 180px;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
            }
            .location {
              font-size: 14px;
              width: 180px;
              color: #808080;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
              margin: 2px 0 0px;
            }
            .distance {
              font-size: 14px;
              color: #808080;
              width: 180px;
              overflow: hidden; //超出的文本隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap; // 默认不换行；
            }
          }
        }
      }
    }
  }
}
.jctj {
  background: #f7f7f7;
  padding-top: 57px;
  padding-bottom: 132px;
  .jctj-item {
    width: 1420px;
    margin: 0 auto;
    .jctj-img {
      height: 63px;
      display: block;
      margin: 0 auto 59px;
    }
    .culture {
      display: flex;
      justify-content: space-between;
    }
  }
}
@media screen and (max-width: 1420px) {
  // .main .main-top {
  //   display: block;
  // }
  .main .main-top .desc {
    max-width: 900px;
    margin: 20px auto 0;
  }
  .crumbs-item .crumbs {
    padding: 0 30px;
  }
  .main .main-top .main-swiper {
    margin-right: 30px;
  }
}
@media screen and (max-width: 1120px) {
  .main .main-bottom {
    display: block;
  }
  .main .main-top {
    display: block;
  }
  .main .main-bottom .scenic {
    width: 100%;
  }
  .main .main-item {
    padding: 0 30px;
  }
  .main .main-bottom .nearby .list {
    display: flex;
    flex-wrap: wrap;
  }
  .main .main-bottom .nearby {
    width: 100%;
    margin-top: 20px;
  }
  .main .main-top .main-swiper {
    width: 100%;
  }
  .main .main-top .main-swiper .swiper1 {
    width: 100%;
  }
}
@media screen and (max-width: 750px) {
  .main {
    padding: 0 0 20px;
  }
  .main .main-bottom .scenic {
    padding: 30px 30px 1px 30px;
  }
  .main .main-top .main-swiper .swiper1 {
    height: max-content;
  }
  .main .main-top .main-swiper .swiper1 .swiper-slide {
    height: auto;
  }
  .main .main-item {
    padding: 0;
  }
  .main .main-top {
    padding: 0;
  }
  .main .main-top .desc {
    padding: 0 30px;
  }
  .main .main-top .desc .title {
    font-size: 24px;
    margin-bottom: 10px;
  }
  .main .main-top .desc .title span {
    width: 25px;
    height: 17px;
    line-height: 17px;
    margin: 6px 0px 0px 10px;
  }
  .main .main-top .desc .introduction {
    font-size: 16px;
    line-height: 30px;
  }
  .main .main-bottom .scenic .title {
    font-size: 20px;
    &::before {
      height: 25px;
    }
  }
  .main .main-bottom .scenic .desc {
    font-size: 16px;
    line-height: 1.5;
    margin: 15px 0 41px;
  }
  .main .main-bottom .nearby .title-top .title-active {
    font-size: 20px;
  }
  .main .main-bottom .nearby .list .list-item .list-desc .name {
    font-size: 16px;
  }
}
@media screen and (max-width: 450px) {
  .main .main-top .desc .introduction {
    display: block;
  }
}
</style>
