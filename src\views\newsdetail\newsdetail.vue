<template>
  <div>
    <Header />
    <div class="banner">
      <img src="../../assets/01.jpg" alt="" />
    </div>
    <div class="crumbs-item">
      <el-breadcrumb class="crumbs" separator-class="el-icon-arrow-right">
        <el-breadcrumb-item
          ><router-link
            style="color: #606266; font-weight: initial"
            :to="{ path: '/' }"
            >首页</router-link
          >
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==10">城市风光</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==7">本地商超</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==39">非遗文化</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==21">健康生活</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==11">图说固始</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==9">公告公示</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==15">政策信息</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==26">固始新闻</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==27">媒体看固始</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==32">单位动态</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==25">乡镇动态</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==45">招商引资</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==41">学习贯彻党的二十大精神</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==56">学习贯彻习近平新时代中国特色社会主义思想主题教育</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==44">三个一批五个着力</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==43">乡村振兴</el-breadcrumb-item>

        <el-breadcrumb-item v-if="con.gory_id==12">根亲文化</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==5">文化节展</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==33">网络中国节</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==40">名人轶事</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==37">历史人物</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==35">资讯</el-breadcrumb-item>

        <el-breadcrumb-item v-if="con.gory_id==35">资讯</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==38">养老</el-breadcrumb-item>
        <el-breadcrumb-item v-if="con.gory_id==4">招聘</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="con">
      <div class="con-item">
        <div class="con-title">{{ con.news_title }}</div>
        <div class="con-b">
          <span>来源：</span>
          <span>{{ con.news_source }}</span
          >&nbsp;&nbsp;&nbsp;&nbsp;
          <span>作者：</span>
          <span>{{ con.news_auto==''?'未知':con.news_auto}}</span
          >&nbsp;&nbsp;&nbsp;&nbsp;
          <span>{{ con.showtime }}</span>
        </div>
        <div v-if="con.vediofile.length>0" style="text-align: center">
          <video :src="con.vediofile[0]" @contextmenu.prevent controlslist="nodownload"  controls="controls"  style="max-width: 100%;"></video>
        </div>
        <div class="desc" v-html="con.news_content"></div>
      </div>
    </div>
    <recommend />
    <Footers />
  </div>
</template>
<script>
import Header from "@/components/header/header.vue";
import Footers from "@/components/footers/footers.vue";
import recommend from "@/components/recommend/recommend.vue";
import { details } from "@/api/api.js";
export default {
  data() {
    return {
      con: {},
    };
  },
  mounted() {
    this.getDetail();
  },
  watch: {
    $route: {
      // 监听路由的参数变化
      handler() {
        // 数据变化更新数据
        this.getDetail();
      },
      immediate: true, // 立即执行
    },
  },
  computed: {},
  methods: {
    getDetail() {
      let id = this.$route.params.id;
      details(id).then((res) => {
        // console.log(res);
        this.con = res.data.data;
      });
    },
  },
  components: {
    Header,
    Footers,
    recommend,
  },
};
</script>
<style lang="scss">
.desc {
  line-height: 45px;
  img {
    max-width: 100% !important;
  }
  video {
    max-width: 100% !important;
  }
  .note-video-clip {
    max-width: 100% !important;
  }
}
</style>
<style scoped lang="scss">
.banner {
  // height: 360px;
  width: 100%;
  img {
    // height: 360px;
    width: 100%;
    min-height: 140px;
    object-fit: cover;
    display: block;
  }
}
.crumbs-item {
  background: #f7f7f7;
  border-bottom: 1px solid #cccccc;
  .crumbs {
    height: 50px;
    max-width: 1420px;
    margin: 0 auto;
    line-height: 50px;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
  }
}
.con {
  padding: 60px 0 110px;
  background: #fff;
  .con-item {
    max-width: 1420px;
    margin: 0 auto;

    .con-title {
      font-size: 32px;
      font-weight: 400;
      color: #333333;
      text-align: center;
    }
    .con-b {
      margin: 19px 0 58px;
      text-align: center;
      font-size: 20px;
      font-weight: 400;
      color: #808080;
    }
    .desc {
      line-height: 45px;
      img {
        max-width: 100% !important;
      }
      video {
        max-width: 100% !important;
      }
    }
  }
}
@media screen and (max-width: 1480px) {
  .con {
    padding: 60px 30px 110px;
  }
  .crumbs-item .crumbs {
    padding: 0 30px;
  }
}
@media screen and (max-width: 1120px) {
  .crumbs-item .crumbs {
    padding: 0 30px;
  }
  .con {
    padding: 60px 30px 110px;
  }
}
@media screen and (max-width: 750px) {
  .con .con-item .con-b {
    font-size: 15px;
    margin: 19px 0 19px;
  }
  .con .con-item .con-title {
    font-size: 24px;
  }
  .con {
    padding: 30px 30px 30px;
  }
  .con .con-item .desc {
    line-height: 1.7;
  }
}
</style>
