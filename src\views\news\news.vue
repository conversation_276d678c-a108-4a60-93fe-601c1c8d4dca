<template>
  <div>
    <Header/>
    <div class="banner">
      <img src="@/assets/01.jpg" alt=""/>
    </div>
    <div class="crumbs-item">
      <el-breadcrumb class="crumbs" separator-class="el-icon-arrow-right">
        <el-breadcrumb-item
        >
          <router-link
            style="color: #606266; font-weight: initial"
            :to="{ path: '/' }"
          >首页
          </router-link
          >
        </el-breadcrumb-item>
        <el-breadcrumb-item>{{
            pid == '14' ? '时政要闻' : '专题专栏'
          }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="main_item">
      <div class="main_item_ul">
        <ul>
          <li
            @click="getMoreFun(item.id)"
            v-for="item in lists"
            :key="item.id"
            :class="item.id == num ? 'main_item-active' : ''"
          >
            {{ item.name }}
          </li>
        </ul>
      </div>
      <div class="lists_box">
        <template v-if="newsList"   v-for="item in newsList.slice(
              (currentPage - 1) * pageSize,
              currentPage * pageSize
            )">
          <div
            class="lists"

            :key="item.id"
            @click="goDetail(item.id)"
          >
            <img
              :src="item.news_image[0]"
              alt=""
              v-if="item.news_image.length > 0"
            />
            <div
              class="lists_right"
              :class="
                item.news_image.length < 1 ? 'lists_right1' : 'lists_right'
              "
            >
              <div class="data">{{ item.showtime }}</div>
              <div class="lists_title">{{ item.news_title }}</div>
              <div class="data data4">{{ item.showtime }}</div>
              <el-button class="btn" size="mini" type="info" round
              >查看详情
              </el-button
              >
            </div>
          </div>
          <div style="background-color: #DFE1E5;height: 1px;width: 100%;"></div>
        </template>
        <template v-if="newsList.length == 0">
          <el-empty :image-size="200"></el-empty>
        </template>
        <div class="fenye">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :current-page="currentPage"
            :page-size="pageSize"
            @current-change="current_change"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- <recommend /> -->
    <Footers/>
  </div>
</template>
<script>
import Header from '@/components/header/header.vue'
import Footers from '@/components/footers/footers.vue'
import recommend from '@/components/recommend/recommend.vue'
import { currentPolitics, navigation } from '@/api/api.js'

export default {
  data () {
    return {
      page: 1,
      pageSize: 15,
      newsList: [],
      total: 1,
      currentPage: 1,
      lists: [],
      i: 0,
      id: 0,
      num: 0,
      pid: 0,
    }
  },
  mounted () {
    let id = this.$route.params.id
    let pid = this.$route.params.pid
    console.log(id)
    console.log(pid)
    this.pid = pid
    this.getNavigation(pid)
    this.getMoreFun(id)
  },
  watch: {
    $route: {
      // 监听路由的参数变化
      handler () {
        // 数据变化更新数据
        let id = this.$route.params.id
        let pid = this.$route.params.pid
        this.pid = pid
        this.getNavigation(pid)
        this.getMoreFun(id)
      },
      immediate: true, // 立即执行
    },
  },
  computed: {},
  methods: {
    getMoreFunClick (info) {
      if (info.diyname == '') {
        return
      } else {
        window.open(info.diyname)
      }
    },
    async getMoreFun (id) {
      const index = this.lists.findIndex(obj => obj.id === id)
      const info = this.lists[index]
      console.log(info)
      if (typeof (info) != 'undefined' && info.diyname != '') {
        return
      }
      this.num = id
      const { data: res } = await currentPolitics(id)
      // res.data.news.forEach((item) => {
      //   if (item.news_image) {
      //     return (item.news_image[0] = item.news_image[0].replace(
      //       "http://cs2.0rui.cn",
      //       "http://kl.lytv.com.cn"
      //     ));
      //   } else {
      //     return item;
      //   }
      // });
      this.newsList = res.data.news
      this.total = this.newsList.length
      this.currentPage = 1
    },
    getNavigation (pid) {
      navigation(pid).then((res) => {
        // console.log(res);
        this.lists = res.data.data
      })
    },
    goDetail (id) {
      const route = this.$router.resolve({
        path: `/newsdetail/${id}`
      });
      window.open(route.href, '_blank');
    },
    current_change (currentPage) {
      //改变当前页
      this.currentPage = currentPage
      window.scrollTo(0, 0)
    },
  },
  components: {
    Header,
    Footers,
    recommend,
  },
}
</script>
<style>
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background: linear-gradient(0deg, #bf0614, #ff4c4c);
}
</style>
<style scoped lang="scss">
.banner {
  // height: 360px;
  width: 100%;

  img {
    // height: 360px;
    min-height: 140px;
    width: 100%;
    object-fit: cover;
    display: block;
  }
}

.crumbs-item {
  background: #f7f7f7;
  border-bottom: 1px solid #cccccc;

  .crumbs {
    height: 50px;
    max-width: 1420px;
    margin: 0 auto;
    line-height: 50px;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
  }
}

.main_item {
  background: #f7f7f7;
  padding: 30px 0 60px;

  .main_item-active {
    background: linear-gradient(0deg, #bf0614, #ff4c4c);
    color: #fff;
  }

  ul {
    display: flex;
    justify-content: flex-start;
    max-width: 1420px;
    margin: 0 auto 40px;
    flex-wrap: wrap;

    li {
      padding: 0 25px;
      background: #e6e6e6;
      border-radius: 100px;
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      margin: 0 20px;
      box-sizing: border-box;
      height: 40px;
      line-height: 40px;
      margin-top: 10px;
      cursor: pointer;
    }
  }

  .lists_box {
    max-width: 1420px;
    margin: 0 auto;
    @keyframes myfirst {
      0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
      }
      100% {
        opacity: 1;
        -webkit-transform: translateX(0);
      }
    }
    /*使用动画：将动画绑定元素上*/
    .lists {
      animation: myfirst 1s;
      -moz-animation: myfirst 1s; /* Firefox */
      -webkit-animation: myfirst 1s; /* Safari 和 Chrome */
      -o-animation: myfirst 1s; /* Opera */
    }

    .lists {
      padding: 20px 30px;
      display: flex;
      align-items: center;
      background: #ffffff;
      //box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
      //border-radius: 8px;
      //margin-bottom: 30px;
      transition: all 0.3s;
      //border-bottom: 1px solid #999999;
      cursor: pointer;

      &:hover {
        transform: scale(1.005);
        box-shadow: 0 5px 30px rgb(0 0 0 / 10%);
      }

      &:hover .lists_right .btn {
        background: linear-gradient(0deg, #bf0614, #ff4c4c);
        color: #fff;
      }

      img {
        max-height: 200px;
        max-width: 200px;
        object-fit: cover;
        margin-right: 20px;
        border-radius: 6px;
      }

      .lists_right {
        .btn {
          background: #e6e6e6;
          color: #808080;
          border: none;
          // width: 120px;
          // height: 40px;
        }

        .data {
          font-size: 16px;
          font-weight: 400;
          color: #808080;
        }

        .data4 {
          display: none;
        }

        .lists_title {
          font-size: 20px;
          font-weight: 400;
          color: #333333;
          margin: 15px 0 15px;
        }

        .data1 {
          margin: 0 0 20px;
          font-size: 20px;
          font-weight: 400;
          color: #808080;
        }
      }

      .lists_right1 {
        .data {
          font-size: 16px;
          font-weight: 400;
          color: #808080;
          margin: 0 0 10px;
        }

        .lists_title {
          font-size: 20px;
          font-weight: 400;
          color: #333333;
          margin-bottom: 15px;
        }
      }
    }

    .fenye {
      display: flex;
      justify-content: center;
    }
  }
}

@media screen and (max-width: 1480px) {
  .crumbs-item .crumbs {
    padding: 0 30px;
  }
  .main_item {
    padding: 30px 30px 60px;
  }
}

@media screen and (max-width: 1120px) {
  .crumbs-item .crumbs {
    padding: 0 30px;
  }
  .main_item {
    padding: 30px 30px 60px;
  }
}

@media screen and (max-width: 750px) {
  .main_item .main_item_ul {
    overflow: hidden;
    padding: 0 20px;
  }
  .main_item ul {
    margin: 0px auto 10px;
    padding-bottom: 15px;
    flex-wrap: nowrap;
    max-width: inherit;
    width: auto;
    overflow-x: scroll;
  }
  .main_item ul li {
    flex-shrink: 0;
    margin: 0 10px;
  }
}

@media screen and (max-width: 450px) {
  .main_item .lists_box .lists {
    // display: block;
    box-shadow: none;
    align-items: flex-start;
    margin-bottom: 10px;
    padding: 12px 18px;
    animation: none;
  }
  .main_item .lists_box .lists img {
    width: 100%;
    max-width: 100%;
    max-height: auto;
  }
  .main_item {
    padding: 30px 5px 40px;
  }
  .main_item .lists_box .lists img {
    max-width: 138px;
    max-height: 92px;
  }
  .main_item .lists_box .lists .lists_right .btn {
    display: none;
  }
  .main_item .lists_box .lists .lists_right .lists_title {
    margin: 0;
    overflow: hidden; //超出的文本隐藏
    display: -webkit-box;
    -webkit-line-clamp: 2; // 超出多少行
    -webkit-box-orient: vertical;
    font-size: 18px;
  }
  .main_item .lists_box .lists .lists_right .data {
    display: none;
  }
  .main_item .lists_box .lists .lists_right .data4 {
    display: block;
    margin-top: 10px;
    font-size: 16px;
  }
}
</style>
