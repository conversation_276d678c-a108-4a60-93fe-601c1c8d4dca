<template>
  <div>
    <Header />
    <div class="content">
      <div class="item">
        <div class="title">
          <div>账号注册</div>
          <div>已有账号，<span @click="goLogin()">立即登录</span></div>
        </div>
        <div class="login">
          <div class="l">
            <!-- <p>Welcome</p>
            <p>固始电视台</p> -->
            <img src="../../assets/welcone.png" alt="" />
          </div>
          <div class="r">
            <el-input
              prefix-icon="el-icon-phone"
              class="inp"
              v-model="mobile"
              placeholder="请输入手机号"
            >
            </el-input>
            <el-input
              prefix-icon="el-icon-s-custom"
              class="inp1"
              v-model="username"
              placeholder="请输入用户名"
            >
            </el-input>
            <el-input
              prefix-icon="el-icon-s-home"
              v-model="email"
              placeholder="请输入邮箱"
            >
              <!-- <el-button class="yz" slot="append" v-if="show" @click="send()"
                >发送验证</el-button
              >
              <el-button class="yz" slot="append" v-if="!show" @click="send()"
                >{{ count }}s重新获取</el-button
              > -->
            </el-input>
            <form>
              <el-input
                class="confirmPassword"
                prefix-icon="el-icon-view"
                v-model="password"
                placeholder="请输入密码"
                show-password
                type="password"
              ></el-input>
            </form>
            <el-button class="btn" type="primary" @click="getRegister()"
              >注册</el-button
            >
          </div>
        </div>
      </div>
    </div>
    <Footers />
  </div>
</template>
<script>
import Header from "@/components/header/header.vue";
import Footers from "@/components/footers/footers.vue";
import { register } from "@/api/api.js";
export default {
  data() {
    return {
      mobile: "",
      email: "",
      password: "",
      count: "",
      show: true,
      username: "",
      timer: null,
    };
  },
  mounted() {},
  computed: {},
  methods: {
    goLogin() {
      this.$router.push({ name: "login" });
    },
    send() {
      const TIME_COUNT = 60;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.show = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.show = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },
    getRegister() {
      let data = {
        username: this.username,
        password: this.password,
        email: this.email,
        mobile: this.mobile,
      };
      register(data).then((res) => {
        // console.log(res);
        if (res.data.code == 200) {
          this.$message({
            message: res.data.msg,
            type: "success",
          });
          this.$router.push({ name: "login" });
        } else {
          this.$message({
            message: res.data.msg,
            type: "error",
          });
        }
      });
    },
  },
  components: {
    Header,
    Footers,
  },
};
</script>
<style scoped lang="scss">
.content {
  padding: 100px 0 100px;
  background: url("../../assets/loginbj.png");
  .item {
    max-width: 800px;
    margin: 0 auto;
    .title {
      width: 100%;
      font-size: 20px;
      font-weight: bold;
      color: #0f4b9d;
      background-color: #fff;
      padding: 20px 40px;
      display: flex;
      justify-content: space-between;
      div {
        &:nth-child(2) {
          font-size: 12px;
          color: #808080;
        }
        span {
          color: #0f4b9d;
          cursor: pointer;
        }
      }
    }
    .login {
      display: flex;
      padding: 20px 40px;
      background-color: #fff;
      justify-content: center;
      .l {
        width: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 80%;
        }
      }
      .r {
        width: 50%;
        .inp {
          margin-bottom: 20px;
        }
        .inp1 {
          margin-bottom: 20px;
        }
        .confirmPassword {
          margin-top: 20px;
        }
        .yz {
          color: #0f4b9d;
          font-weight: bold;
        }
        .btn {
          width: 70%;
          margin: 20px 0 60px;
        }
      }
    }
  }
}
@media screen and (max-width: 750px) {
  .content {
    padding: 100px 30px 100px;
  }
}
@media screen and (max-width: 450px) {
  .content .item .login .l {
    display: none;
  }
  .content .item .login {
    padding: 20px 0px;
  }
  .content .item .login .r {
    width: 85%;
  }
  .content .item .login .r .btn {
    width: 100%;
    margin: 20px 0 10px;
  }
}
</style>
