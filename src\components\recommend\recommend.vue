<template>
  <div class="r">
    <div class="recommend">
      <img class="recommend-img" src="../../assets/jctj.png" alt="" />
      <div class="recommend-item" v-if="exhibitionList">
        <newsImgList
          :btwhTitle="exhibitionTitle"
          :newsImgList="exhibitionList"
          style="margin-top: 20px"
          :id="5"
          :pid="18"
        />
        <newsImgList
          style="margin-top: 20px"
          :btwhTitle="relativeTitle"
          :newsImgList="relativeList"
          :id="12"
          :pid="18"
        />
        <newsImgList
          style="margin-top: 20px"
          :btwhTitle="festivalTitle"
          :newsImgList="festivalList"
          :id="33"
          :pid="18"
        />
      </div>
    </div>
  </div>
</template>
<script>
import newsImgList from "@/components/newsImgList/newsImgList.vue";
import { navigation, currentPolitics } from "@/api/api.js";
export default {
  data() {
    return {
      exhibitionList: [], //文化节展
      exhibitionTitle: "", //媒体看固始
      relativeList: [], //根亲文化
      relativeTitle: "", //媒体看固始
      festivalList: [], //网络中国节
      festivalTitle: "", //媒体看固始
      featureList: [], //专题专栏
      featureTitle: "", //媒体看固始
    };
  },
  mounted() {
    this.getGsrw();
  },
  computed: {},
  methods: {
    getCurrentPolitics(id) {
      currentPolitics(id).then((res) => {
        switch (id) {
          case 5:
            console.log(res);
            this.exhibitionTitle = res.data.data.name;
            this.exhibitionList = res.data.data.news;
            break;
          case 12:
            this.relativeTitle = res.data.data.name
            console.log(res.data.data.news);
            var yourArray= res.data.data.news;
            // 使用filter()方法来删除news_image为空的元素
            var filteredArray = yourArray.filter(function(item) {
              return item.news_image !== '';
            });
            // 如果需要重排数组，你可以使用sort()方法
            filteredArray.sort(function(a, b) {
              // 这里可以根据你的排序需求进行比较
              // 例如，按content属性升序排序
              return a.id - b.id;
            });
            this.relativeList = filteredArray;
            break;
          case 33:
            this.festivalTitle = res.data.data.name;
            this.festivalList = res.data.data.news;
            break;
        }
      });
    },
    getGsrw() {
      navigation(18).then((res) => {
        res.data.data.forEach((item) => {
          this.getCurrentPolitics(item.id);
        });
      });
    },
  },
  components: {
    newsImgList,
  },
};
</script>
<style scoped lang="scss">
.r {
  background: url("../../assets/tuijianbj.jpg") no-repeat;
  background-position: center center;
  background-size: 100% 110%;
}
.recommend {
  max-width: 1420px;
  margin: 0 auto;
  padding: 57px 0 132px;
  .recommend-img {
    display: block;
    margin: 0 auto 39px;
    max-width: 599px;
    width: 100%;
    object-fit: cover;
  }
  .recommend-item {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
}
@media screen and (max-width: 1480px) {
  .recommend {
    padding: 57px 30px 132px;
  }
}
@media screen and (max-width: 1120px) {
  .recommend {
    padding: 57px 30px 132px;
  }
}
@media screen and (max-width: 750px) {
  .recommend {
    padding: 30px 30px 30px;
  }
  .recommend .recommend-img {
    margin: 0 auto 0px;
  }
}
</style>
