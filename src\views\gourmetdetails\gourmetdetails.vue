<template>
  <div>
    <Header/>
    <div class="banner">
      <img src="../../assets/01.jpg" alt=""/>
    </div>
    <div class="item">
      <el-breadcrumb class="crumbs" separator-class="el-icon-arrow-right">
        <el-breadcrumb-item
        >
          <router-link
            style="color: #606266; font-weight: initial"
            :to="{ path: '/' }"
          >首页
          </router-link
          >
        </el-breadcrumb-item>
        <el-breadcrumb-item>固始美食</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="main">
      <div class="main-item">
        <div class="pic">
          <div class="bigpic">
            <!-- <img :src="bigpicImg" alt="" /> -->
            <div class="swiper-container gallery-top">
              <div class="swiper-wrapper">
                <div
                  class="swiper-slide"
                  v-for="(item, index) in con.news_image"
                  :key="index"
                >
                  <img :src="item" alt=""/>
                </div>
              </div>
            </div>
          </div>
          <div class="swiper-container gallery-thumbs swiper1">
            <div class="swiper-wrapper">
              <div
                class="swiper-slide"
                v-for="(item, index) in con.news_image"
                :key="index"
                :data-attr="con.news_title"
              >
                <img :src="item" alt=""/>
              </div>
            </div>
          </div>
          <div class="swiper-container swiper2">
            <div class="swiper-wrapper">
              <div
                class="swiper-slide"
                v-for="(item, index) in con.news_image"
                :key="index"
                :data-attr="'- ' + con.news_title + ' -'"
              >
                <img :src="item" alt=""/>
              </div>
            </div>
          </div>
        </div>
        <div class="main-content">
          <div class="finefood">
            <div class="title">美食介绍</div>
            <div class="desc" v-html="con.news_content"></div>
            <div class="title">美食做法</div>
            <div class="desc" v-html="con.policy"></div>
          </div>
          <div class="merchant">
            <div class="title">推荐商家</div>
            <div class="list">
              <div class="list-item" v-for="item in hotelList" :key="item.id">
                <img :src="item.images[0]" alt=""/>
                <div class="desc">
                  <div class="name">{{ item.name }}</div>
                  <div class="location">
                    {{ item.address }}
                  </div>
                  <el-rate
                    v-model="item.level"
                    disabled
                    disabled-void-color="#CCCCCC"
                    :colors="['#BF0614', '#BF0614', '#BF0614']"
                  >
                  </el-rate>
                </div>
              </div>
              <!-- <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="desc">
                  <div class="name">小园林酒楼总店</div>
                  <div class="location">
                    固始县陈元光大道南固始县陈元光大道南
                  </div>
                  <el-rate
                    v-model="value"
                    disabled
                    disabled-void-color="#CCCCCC"
                    :colors="['#BF0614', '#BF0614', '#BF0614']"
                  >
                  </el-rate>
                </div>
              </div>
              <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="desc">
                  <div class="name">小园林酒楼总店</div>
                  <div class="location">
                    固始县陈元光大道南固始县陈元光大道南
                  </div>
                  <el-rate
                    v-model="value"
                    disabled
                    disabled-void-color="#CCCCCC"
                    :colors="['#BF0614', '#BF0614', '#BF0614']"
                  >
                  </el-rate>
                </div>
              </div>
              <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="desc">
                  <div class="name">小园林酒楼总店</div>
                  <div class="location">
                    固始县陈元光大道南固始县陈元光大道南
                  </div>
                  <el-rate
                    v-model="value"
                    disabled
                    disabled-void-color="#CCCCCC"
                    :colors="['#BF0614', '#BF0614', '#BF0614']"
                  >
                  </el-rate>
                </div>
              </div>
              <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="desc">
                  <div class="name">小园林酒楼总店</div>
                  <div class="location">
                    固始县陈元光大道南固始县陈元光大道南
                  </div>
                  <el-rate
                    v-model="value"
                    disabled
                    disabled-void-color="#CCCCCC"
                    :colors="['#BF0614', '#BF0614', '#BF0614']"
                  >
                  </el-rate>
                </div>
              </div>
              <div class="list-item">
                <img src="../../assets/gsek.png" alt="" />
                <div class="desc">
                  <div class="name">小园林酒楼总店</div>
                  <div class="location">
                    固始县陈元光大道南固始县陈元光大道南
                  </div>
                  <el-rate
                    v-model="value"
                    disabled
                    disabled-void-color="#CCCCCC"
                    :colors="['#BF0614', '#BF0614', '#BF0614']"
                  >
                  </el-rate>
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <recommend/>
    <Footers/>
  </div>
</template>
<script>
import Header from '@/components/header/header.vue'
import Footers from '@/components/footers/footers.vue'
import Swiper from 'swiper'
import newsImgList from '@/components/newsImgList/newsImgList.vue'
import recommend from '@/components/recommend/recommend.vue'
import {
  navigation,
  currentPolitics,
  banner,
  details,
  hotel,
} from '@/api/api.js'

export default {
  name: 'gourmetdetails',
  data () {
    return {
      sImgList: [],
      bigpicImg: '',
      value: 4.5,
      picFlag: false,
      picFlag1: true,
      picIndex: 1,
      con: {},
      bigpicImgList: [],
      hotelList: [],
    }
  },
  mounted () {
    this.getDetail()
    this.getHotel()
  },
  computed: {},
  methods: {
    fun_str (item) {
      return JSON.stringify(item)
    },
    initSwiper1 (arr) {
      let that = this
      new Swiper('.swiper1', {
        direction: 'vertical', // 垂直切换选项
        loop: true, // 循环模式选项
        slidesPerView: 3,
        spaceBetween: 8,
        mousewheel: false,
        slideToClickedSlide: true,
        centeredSlides: true,
        initialSlide: 1,
        loopAdditionalSlides: 100,
        allowTouchMove: false,
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        on: {
          click: function (e) {
            // console.log(this.realIndex);
            if (this.clickedSlide) {
              let item = JSON.parse(
                this.clickedSlide.attributes['data-href'].nodeValue
              )
              that.picIndex = this.realIndex
              that.bigpicImg = arr[item]
            }
          },
        },
      })
    },
    getPic (index) {
      this.picIndex = index
    },
    initSwiper2 () {
      let that = this
      var galleryThumbs = new Swiper('.gallery-thumbs', {
        direction: 'vertical',
        spaceBetween: 10,
        slidesPerView: 3,
        // mousewheel: false,
        loop: true,
        centeredSlides: true,
        slideToClickedSlide: true,
        allowTouchMove: false,
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        freeMode: false,
        loopedSlides: 5, //looped slides should be the same
        // watchSlidesVisibility: true,
        // watchSlidesProgress: true,
      })
      var galleryTop = new Swiper('.gallery-top', {
        direction: 'vertical',
        loop: true,
        // loopedSlides: 5, //looped slides should be the same
        effect: 'fade',
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        freeMode: false,
        thumbs: {
          swiper: galleryThumbs,
        },
      })
    },
    initSwiper3 () {
      let that = this
      var galleryThumbs = new Swiper('.swiper2', {
        spaceBetween: 10,
        slidesPerView: 3,
        // mousewheel: false,
        loop: true,
        centeredSlides: true,
        slideToClickedSlide: true,
        allowTouchMove: false,
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        freeMode: false,
        loopedSlides: 5, //looped slides should be the same
        // watchSlidesVisibility: true,
        // watchSlidesProgress: true,
      })
      var galleryTop = new Swiper('.gallery-top', {
        direction: 'vertical',
        loop: true,
        // loopedSlides: 5, //looped slides should be the same
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        effect: 'fade',
        thumbs: {
          swiper: galleryThumbs,
        },
      })
    },
    getDetail () {
      let id = this.$route.params.id
      details(id).then((res) => {
        this.con = res.data.data
        this.bigpicImgList = res.data.data.news_image
        this.bigpicImg = res.data.data.news_image[1]
        this.$nextTick(() => {
          // this.initSwiper1(this.bigpicImgList);
          this.initSwiper2()
          this.initSwiper3()
        })
      })
    },
    getHotel () {
      hotel(2).then((res) => {
        // console.log(res);
        this.hotelList = res.data.data
      })
    },
  },
  components: {
    Header,
    Footers,
    newsImgList,
    recommend,
  },
}
</script>
<style>
.el-rate__icon {
  margin: 0px !important;
}
</style>
<style scoped lang="scss">

.aa {
  display: none;
}

.banner {
  // height: 360px;
  width: 100%;

  img {
    // height: 360px;
    width: 100%;
    object-fit: cover;
    display: block;
    min-height: 140px;
  }
}

.item {
  background: #f7f7f7;
  border-bottom: 1px solid #cccccc;

  .crumbs {
    height: 50px;
    max-width: 1420px;
    margin: 0 auto;
    line-height: 50px;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
  }
}

.main {
  padding: 40px 0 60px;

  .main-item {
    max-width: 1420px;
    margin: 0 auto;

    .pic {
      display: block;
      background: #f7f7f7;
      padding: 30px;
      display: flex;
      justify-content: space-between;
      height: auto;

      .bigpic {
        width: 67%;
        margin-right: 10px;

        .gallery-top {
          width: 100%;
          height: 504px;
        }

        img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .swiper2 {
        display: none;

        .swiper-slide {
          width: 100%;
          height: 162px;
          cursor: pointer;
          position: relative;

          &::after {
            content: attr(data-attr);
            display: block;
            position: absolute;
            width: 100%;
            height: 162px;
            font-size: 32px;
            font-weight: 400;
            color: #fff;
            text-align: center;
            line-height: 162px;
            background: rgba(0, 0, 0, 0.4);
            top: 0;
            left: 0;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .swiper-slide-active {
          &::after {
            display: none;
          }
        }
      }

      .swiper1 {
        width: 33%;
        height: 504px;

        .swiper-slide {
          width: 100%;
          height: 162px;
          cursor: pointer;
          position: relative;

          &::after {
            content: attr(data-attr);
            display: block;
            position: absolute;
            width: 100%;
            height: 162px;
            font-size: 32px;
            font-weight: 400;
            color: #fff;
            text-align: center;
            line-height: 162px;
            background: rgba(0, 0, 0, 0.4);
          }

          .sw2 {
            width: 100%;
            height: 162px;

            div {
              width: 100%;
              height: 162px;
              background: rgba(0, 0, 0, 0.4);
              color: #fff;
              font-size: 32px;
              color: #ffffff;
              line-height: 162px;
              text-align: center;
            }
          }
        }

        .swiper-slide-active {
          &::after {
            display: none;
          }
        }

        img {
          width: 100%;
          height: 162px;
          object-fit: cover;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: -5;
        }

        .swiper-name {
          width: 440px;
          height: 162px;
          font-size: 32px;
          font-weight: 400;
          text-align: center;
          line-height: 162px;
          color: #ffffff;
          background: rgba(0, 0, 0, 0.4);
        }
      }
    }

    .main-content {
      max-width: 1420px;
      margin-top: 20px;
      display: flex;
      justify-content: space-between;

      .finefood {
        width: 1060px;
        background: #f7f7f7;
        padding: 30px 30px 10px 30px;
        margin-right: 20px;
        height: fit-content;

        .title {
          display: flex;
          font-size: 32px;
          font-weight: 400;
          align-items: center;
          color: #333333;

          &::before {
            content: "";
            display: block;
            width: 11px;
            height: 37px;
            background: linear-gradient(-90deg, #bf0614, #ff4c4c);
            margin-right: 9px;
          }
        }

        .desc {
          font-size: 20px;
          font-weight: 400;
          color: #333333;
          line-height: 45px;
          margin: 30px 50px;
          text-align: justify;
          text-justify: distribute;
        }
      }

      .merchant {
        width: 340px;
        background: #f7f7f7;
        padding: 28px;
        height: max-content;
        position: relative;

        .title {
          font-size: 24px;
          font-weight: 400;
          color: #333333;

          &::after {
            content: "";
            display: block;
            width: 98px;
            height: 2px;
            background: linear-gradient(-90deg, #bf0614, #ff4c4c);
            border-radius: 1px;
          }
        }

        .list {
          .list-item {
            margin-top: 20px;
            display: flex;

            img {
              width: 90px;
              height: 72px;
              display: block;
              margin-right: 10px;
              object-fit: cover;
            }

            .desc {
              line-height: 24px;

              .name {
                font-size: 20px;
                font-weight: 400;
                color: #333333;
              }

              .location {
                font-size: 16px;
                font-weight: 400;
                width: 180px;
                color: #808080;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; // 默认不换行；
              }
            }
          }
        }
      }
    }
  }
}

.jctj {
  background: #f7f7f7;
  padding-top: 57px;
  padding-bottom: 132px;

  .jctj-item {
    width: 1420px;
    margin: 0 auto;

    .jctj-img {
      height: 63px;
      display: block;
      margin: 0 auto 59px;
    }

    .culture {
      display: flex;
      justify-content: space-between;
    }
  }
}

@media screen and (max-width: 1480px) {
  .item .crumbs {
    padding: 0 30px;
  }
}

@media screen and (max-width: 1120px) {
  .main .main-item .main-content {
    display: block;
    padding: 0 30px;
  }
  .main .main-item .main-content .finefood {
    margin: 0 auto;
    width: 100%;
  }
  .main .main-item .main-content .merchant {
    width: 100%;
    margin-top: 20px;
  }
  .main .main-item .main-content .merchant .list {
    display: flex;
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 750px) {
  .main {
    padding: 0 0 20px;
  }
  .main .main-item .pic {
    display: block;
    padding: 0;
  }
  .main .main-item .pic .bigpic {
    width: 100%;
  }
  .main .main-item .pic .swiper2 {
    display: block;
  }
  .main .main-item .pic .swiper1 {
    display: none;
  }
  .main .main-item .pic .bigpic .gallery-top {
    height: 67vw;
  }
  .main .main-item .pic .swiper2 .swiper-slide {
    height: 21.5vw;

    &::after {
      height: 21.5vw;
      line-height: 21.5vw;
      font-size: 5vw;
    }
  }
  .main .main-item .main-content .finefood .desc {
    font-size: 16px;
    line-height: 1.5;
    margin: 15px 0px 40px;
    text-indent: 2em;
  }
  .main .main-item .main-content .finefood .title {
    font-size: 20px;

    &::before {
      height: 25px;
    }
  }
  .main .main-item .main-content {
    padding: 0;
  }
  .main .main-item .main-content .merchant .title {
    font-size: 20px;

    &::after {
      width: 85px;
    }
  }
  .main .main-item .main-content .merchant .list .list-item .desc .name {
    font-size: 16px;
  }
  .main .main-item .main-content .merchant .list .list-item .desc .location {
    font-size: 14px;
  }
}
</style>
