<template>
  <div class="news-box">
    <div class="news-box-top">
      <div>{{ szywTitle }}</div>
      <div @click="goNewsList(id, pid)">更多></div>
    </div>
    <ul>
      <li v-for="item in newsList" :key="item.id" @click="goDetail(item.id)">
        <!-- <span>{{ item.news_title }}</span> -->
        <!-- <span>{{ item.showtime }}</span> -->
        <div class="li-item">
          <div>{{ item.news_title }}</div>
          <div>{{ item.showtime.slice(0, 10) }}</div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: "newsList",
  props: {
    szywTitle: {
      type: String,
      default: "",
    },
    newsList: {
      type: Array,
      default: [],
    },
    id: {
      type: Number,
      default: 0,
    },
    pid: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
  mounted() {
  },
  computed: {},
  methods: {
    goDetail(id) {
      this.$router.push({
        path: `/newsdetail/${id}`,
      });
    },
    goNewsList(id, pid) {
      this.$router.push({
        path: `/news/${id}/${pid}`,
      });
    },
  },
};
</script>
<style scoped lang="scss">
.news-box {
  width: 49%;
  height: 472px;
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;

  .news-box-top {
    width: 100%;
    height: 60px;
    background: #f7f7f7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 23px 0 20px;
    div {
      &:nth-child(1) {
        font-size: 24px;
        font-weight: 400;
        color: #bf0614;
      }
      &:nth-child(2) {
        font-size: 16px;
        font-weight: 400;
        color: #808080;
        cursor: pointer;
        &:hover {
          color: #bf0614;
        }
      }
    }
  }
  ul {
    // list-style-type: disc;
    // list-style-position: outside;
    font-size: 20px;
    font-weight: 400;
    color: #333333;
    margin-top: 10px;
    width: 100%;
    li {
      line-height: 45px;
      width: 100%;
      box-sizing: border-box;
      cursor: pointer;
      // display: flex;
      // margin-left: 22px;
      align-items: center;
      display: flex;
      align-items: center;
      // &::before {
      //   content: "";
      //   width: 9px;
      //   height: 9px;
      //   background: #bf0614;
      //   border-radius: 100px;
      //   display: block;
      //   margin-right: 20px;
      //   flex-shrink: 0;
      // }
      transition: 0.5s;
      &::before {
        content: "";
        width: 6px;
        height: 6px;
        display: inline-block;
        border-radius: 6px;
        background: rgba(191, 6, 20, 1);
        margin-right: 10px;
        flex-shrink: 0;
        line-height: 45px;
        // margin-bottom: 3px;
      }
      &:hover {
        color: #bf0614;
        transform: translateX(-10px);
      }
      .li-item {
        display: flex;
        transition: 0.5s;
        justify-content: space-between;
        width: 100%;
        padding-right: 22px;
        // &:hover {
        //   color: #bf0614;
        //   transform: translateX(-10px);
        // }
        div {
          // display: block;
          &:nth-child(1) {
            width: 65%;
            // overflow: hidden; //超出的文本隐藏
            // text-overflow: ellipsis; //溢出用省略号显示
            // white-space: nowrap; // 默认不换行；
            overflow: hidden; //超出的文本隐藏
            display: -webkit-box;
            -webkit-line-clamp: 1; // 超出多少行
            -webkit-box-orient: vertical;
            margin-right: 15px;
          }
          &:nth-child(2) {
            flex-shrink: 0;
          }
        }
      }
      // &::marker {
      //   color: #bf0614;
      // }
      // &:hover {
      //   color: #bf0614;
      // }
    }
  }
}
@media screen and (max-width: 1120px) {
  .news-box {
    margin-top: 20px;
    width: 100%;
  }
}
@media screen and (max-width: 750px) {
  .news-box {
    margin-top: 20px;
    width: 100%;
    height: max-content;
    box-shadow: none;
    margin-bottom: 0;
  }
  .news-box ul {
    font-size: 16px;
  }
  .news-box ul li {
    line-height: 35px;
  }
  .news-box .news-box-top {
    height: 40px;
    div {
      &:nth-child(1) {
        font-size: 20px;
      }
    }
  }
}
</style>
