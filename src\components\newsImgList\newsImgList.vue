<template>
  <div class="news-box">
    <div class="news-box-top">
      <div>{{ btwhTitle }}</div>
      <div @click="goNewsList(id, pid)">更多></div>
    </div>
    <div v-if="newsImgList.length>0" class="news-img" @click="goDetail(newsImgList[0].id)">
      <template v-if="newsImgList[0].news_image">
        <img :src="newsImgList[0].news_image" alt="" />
      </template>
      <div class="news-title">{{ newsImgList[0].news_title }}</div>
    </div>
    <ul>
      <li
        v-for="item in newsImgList.slice(1, 4)"
        :key="item.id"
        @click="goDetail(item.id)"
      >
        <span>{{ item.news_title }}</span>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: "newsList",
  props: {
    btwhTitle: {
      type: String,
      default: "",
    },
    newsImgList: {
      type: Array,
      default: [],
    },
    id: {
      type: Number,
      default: 0,
    },
    pid: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
  mounted() {
    console.log(this.newsImgList);
  },
  computed: {},
  methods: {
    goDetail(id) {
      this.$router.push(
        {
          path: `/newsdetail/${id}`,
        },
        () => {}
      );
    },
    goNewsList(id, pid) {
      this.$router.push({
        path: `/news/${id}/${pid}`,
      });
    },
  },
};
</script>
<style scoped lang="scss">
.news-box {
  width: 32%;
  height: 500px;
  padding: 20px;
  background: #ffffff;
  margin-bottom: 20px;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
  .news-box-top {
    width: 100%;
    display: flex;
    height: 40px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #bfbeba;
    div {
      &:nth-child(1) {
        font-size: 24px;
        font-weight: 400;
        color: #bf0614;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        &::after {
          content: "";
          display: block;
          background: #bf0614;
          height: 3px;
          width: 110px;
          margin-top: 6px;
        }
      }
      &:nth-child(2) {
        font-size: 16px;
        font-weight: 400;
        color: #808080;
        cursor: pointer;
        &:hover {
          color: #bf0614;
        }
      }
    }
  }
  .news-img {
    width: 100%;
    height: 252px;
    margin-top: 20px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    border-radius: 10px;
    img {
      width: 100%;
      height: 252px;
      transition: 1s;
      object-fit: cover;
      display: block;
      &:hover {
        transform: scale(1.1);
      }
    }
    .news-title {
      width: 100%;
      height: 40px;
      background: rgba(0, 0, 0, 0.5);
      font-size: 20px;
      font-weight: 400;
      color: #ffffff;
      position: absolute;
      text-align: center;
      line-height: 40px;
      padding: 0 14px;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
      bottom: 0;
      left: 0;
    }
  }
  ul {
    // list-style-type: disc;
    // list-style-position: inside;
    font-size: 20px;
    font-weight: 400;
    color: #333333;
    margin-top: 10px;
    width: 100%;
    li {
      line-height: 45px;
      width: 100%;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
      box-sizing: border-box;
      cursor: pointer;
      transition: 0.5s;
      &::before {
        content: "";
        width: 6px;
        height: 6px;
        display: inline-block;
        border-radius: 6px;
        background: rgba(191, 6, 20, 1);
        margin-right: 10px;
        flex-shrink: 0;
        line-height: 45px;
        margin-bottom: 3px;
      }
      &:hover {
        color: #bf0614;
        transform: translateX(-10px);
      }
      // span {
      //   transition: 0.5s;
      //   &:hover {
      //     color: #bf0614;
      //     margin-left: -10px;
      //   }
      // }
      // &:hover {
      //   color: #bf0614;
      // }
      // &::marker {
      //   color: #bf0614;
      // }
    }
  }
}
@media screen and (max-width: 1120px) {
  .news-box {
    margin-top: 20px;
    width: 100%;
    height: max-content;
  }
  .news-box .news-box-top {
    width: 100%;
  }
  .news-box .news-img {
    width: 100%;
    height: max-content;
  }
  .news-box .news-img img {
    width: 100%;
    height: max-content;
  }
  .news-box .news-img .news-title {
    width: 100%;
  }
}
@media screen and (max-width: 750px) {
  .news-box .news-img .news-title {
    font-size: 16px;
  }
  .news-box ul {
    font-size: 16px;
  }
  .news-box ul li {
    line-height: 35px;
  }
  .con .con-item .con-title {
    font-size: 24px;
  }
  .con .con-item .con-b {
    margin: 18px 0 18px;
    font-size: 14px;
  }
  .news-box .news-box-top div {
    &::after {
      margin-top: 5px;
    }
  }
  .news-box .news-box-top {
    height: 35px;
  }
  .news-box .news-box-top {
    div {
      &:nth-child(1) {
        font-size: 20px;
      }
    }
  }
  .news-box {
    box-shadow: none;
    margin-bottom: 0px;
  }
}
</style>
