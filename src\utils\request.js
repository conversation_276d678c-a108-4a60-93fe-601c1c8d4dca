import axios from "axios";

// 创建axios的对象
const instance = axios.create({
  baseURL: "https://admin.gsw.gov.cn/api", //配置固定域名
  timeout: 10000,
  headers: {
    "Content-Type": "application/json;charset=UTF-8",
    "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
  }, //给接口添加请求头
  data: "Country=Brasil&City=Belo Horizonte",
});

// 封装get和post请求
export function get(url, params) {
  return instance.get(url, { params });
}

export function post(url, data) {
  return instance.post(url, data);
}

export default instance;
