import request from "@/utils/request"; //导入封装请求的js文件

//导航
export function navigation(pid, flag) {
  return request({
    url: "/news/group", //接口路径
    method: "get", //接口方法
    params: {
      pid: pid,
      flag: flag,
    },
  });
}
//首页背景
export function rightTop(id) {
  return request({
    url: "/news/info", //接口路径
    method: "get", //接口方法
    params: {
      id: id,
    },
  });
}
//首页背景
export function beijing(position, device) {
  return request({
    url: "/index/beijing", //接口路径
    method: "get", //接口方法
    params: {
      position: position,
      device: device,
    },
  });
}

//时政要闻
export function currentPolitics(id) {
  return request({
    url: "/news/groupInfo", //接口路径
    method: "get", //接口方法
    params: {
      id: id,
    },
  });
}

export function currentPoliticsNew(id,page,size) {
  return request({
    url: "/news/groupInfo", //接口路径
    method: "get", //接口方法
    params: {
      id: id,
      page: page,
      size: size,
    },
  });
}

//新闻详情
export function details(id) {
  return request({
    url: "/news/info", //接口路径
    method: "get", //接口方法
    params: {
      id: id,
    },
  });
}

//banner
export function banner(gory_id, flag) {
  return request({
    url: "/news/index", //接口路径
    method: "get", //接口方法
    params: {
      gory_id: gory_id,
      flag: flag,
    },
  });
}
//搜索
export function search(news_content, page, pageSize) {
  return request({
    url: "/news/search", //接口路径
    method: "get", //接口方法
    params: {
      keysword:news_content,
      news_content: news_content,
      page: page,
      pageSize: pageSize,
    },
  });
}

//推荐
export function recommend() {
  return request({
    url: "/news/recommend", //接口路径
    method: "get", //接口方法
  });
}

//推荐酒店/美食
export function hotel(if_type) {
  return request({
    url: "/hotel/index", //接口路径
    method: "get", //接口方法
    params: {
      if_type: if_type,
    },
  });
}

//便民服务
export function service() {
  return request({
    url: "/index/getService", //接口路径
    method: "get", //接口方法
  });
}

//登录
export function login(data) {
  return request({
    url: "/user/login", //接口路径
    method: "post", //接口方法
    data: data,
  });
}

//注册
export function register(data) {
  return request({
    url: "/user/register", //接口路径
    method: "post", //接口方法
    data: data,
  });
}

//忘记密码
export function forget(data) {
  return request({
    url: "/user/emailtest", //接口路径
    method: "post", //接口方法
    data: data,
  });
}

//留言
export function content(data) {
  return request({
    url: "Content/content", //接口路径
    method: "post", //接口方法
    data: data,
  });
}

//固始电子报
export function getDzb(data) {
  return request({
    url: "dzb", //接口路径
    method: "post", //接口方法
    data: data,
  });
}
