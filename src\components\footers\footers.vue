<template>
  <div class="footers">
    <ul>
      <li>
        <a href="https://www.henan.gov.cn/" target="_blank">河南省人民政府</a>
      </li>
      <li>
        <a href="http://www.gushi.gov.cn/gsxrmzf/index.htm" target="_blank">固始县人民政府</a>
      </li>
      <li>
        <a href="https://gd.henan.gov.cn/" target="_blank">河南省广播电视局</a>
      </li>
      <li><a href="https://www.cctv.com/" target="_blank">央视网</a></li>
      <li><a href="http://www.news.cn/" target="_blank">新华网</a></li>
      <li><a href="http://www.people.com.cn/" target="_blank">人民网</a></li>
    </ul>
    <div class="copyright">
<!--      <div id="_ideConac">-->
<!--        <a href="https://bszs.conac.cn/sitename?method=show&id=07FCACA2139AC126E06310291AAC6CFE" target="_blank"><img id="imgConac" vspace="0" hspace="0" border="0" src="https://dcs.conac.cn/image/red.png" data-bd-imgshare-binded="1"></a>-->
<!--      </div>-->
      <div>
        <div>
          <span>增值电信业务经营许可证</span>
          <a style="margin: 0 10px;" href="https://beian.miit.gov.cn/">{{record}} </a>
          <span>著作权保护声明</span>
           </div>
        <div>
          Copyright © 2023 gushiw.com.cn Inc. All Rights Reserved.
          固始县融媒体中心（固始县广播电视台） 版权所有
        </div>
        <div>
          声明：本网部分内容来源于互联网，如有侵权内容请及时
          与网站管理员联系及时删除处理！
        </div>
      </div>
      <div class="tu">
        <div style="display: inline-block;text-align: center;margin-right: 30px;">
          <img src="../../assets/e1.jpg" alt="" style="width: 7rem;"/>
          <div style="">云上固始</div>
        </div>
        <div style="display: inline-block;text-align: center;">
          <img src="../../assets/e2.jpg" alt="" style="width: 7rem;"/>
          <div style="">微信公众号</div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>

export default {
  name: 'footers',
  data () {
    return {
      record:'',
    }
  },
  mounted () {
    var domain = window.location.host;
    console.log(domain);
    if(domain=='gsw.gov.cn'){
      this.record='豫ICP备2024069082号-1';
    }else{
      this.record='豫ICP备2023022635号-3';
    }
  },
  computed: {},
  methods: {},
}
</script>
<style scoped lang="scss">
.tu{
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.footers {
  background: #f7f7f7;

  ul {
    height: 80px;
    display: flex;
    background: #f7f7f7;
    align-items: center;
    justify-content: center;
    border-top: 5px solid #bf0614;
    border-bottom: 1px solid #e6e6e6;
    flex-wrap: wrap;

    li {
      margin-right: 40px;

      &:nth-last-child(1) {
        margin-right: 0;
      }

      a {
        font-size: 20px;
        font-weight: 400;
        color: #808080;

        &:hover {
          color: #bf0614;
        }
      }
    }
  }

  .copyright {
    max-width: 1420px;
    margin: 0 auto;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
    line-height: 28px;
    padding: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 平板设备适配 (768px - 1024px)
@media screen and (max-width: 1024px) and (min-width: 768px) {
  .footers {
    ul {
      height: auto;
      padding: 20px 0;

      li {
        margin-right: 30px;
        margin-bottom: 10px;

        a {
          font-size: 18px;
        }
      }
    }

    .copyright {
      padding: 20px;
      font-size: 14px;
      line-height: 24px;
      flex-direction: column;
      text-align: center;
    }
  }

  .tu {
    margin-top: 15px;

    div {
      margin: 0 15px !important;

      img {
        width: 6rem !important;
      }
    }
  }
}

// 大屏手机适配 (480px - 767px)
@media screen and (max-width: 767px) and (min-width: 480px) {
  .footers {
    ul {
      height: auto;
      padding: 15px 10px;
      flex-direction: column;

      li {
        margin-right: 0;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        a {
          font-size: 16px;
        }
      }
    }

    .copyright {
      padding: 15px;
      font-size: 13px;
      line-height: 22px;
      flex-direction: column;
      text-align: center;

      > div {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .tu {
    margin-top: 10px;
    flex-direction: column;

    div {
      margin: 10px 0 !important;

      img {
        width: 5rem !important;
      }

      div {
        font-size: 14px;
        margin-top: 5px !important;
      }
    }
  }
}

// 小屏手机适配 (320px - 479px)
@media screen and (max-width: 479px) {
  .footers {
    ul {
      height: auto;
      padding: 10px 5px;
      flex-direction: column;

      li {
        margin-right: 0;
        margin-bottom: 6px;

        &:last-child {
          margin-bottom: 0;
        }

        a {
          font-size: 14px;
        }
      }
    }

    .copyright {
      padding: 10px;
      font-size: 12px;
      line-height: 20px;
      flex-direction: column;
      text-align: center;

      > div {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        div {
          margin-bottom: 5px;
        }
      }
    }
  }

  .tu {
    margin-top: 8px;
    flex-direction: column;

    div {
      margin: 8px 0 !important;

      img {
        width: 4rem !important;
      }

      div {
        font-size: 12px;
        margin-top: 3px !important;
      }
    }
  }
}
</style>
