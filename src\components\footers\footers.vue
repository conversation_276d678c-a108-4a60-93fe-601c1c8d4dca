<template>
  <div class="footers">
    <ul>
      <li>
        <a href="https://www.henan.gov.cn/" target="_blank">河南省人民政府</a>
      </li>
      <li>
        <a href="http://www.gushi.gov.cn/gsxrmzf/index.htm" target="_blank">固始县人民政府</a>
      </li>
      <li>
        <a href="https://gd.henan.gov.cn/" target="_blank">河南省广播电视局</a>
      </li>
      <li><a href="https://www.cctv.com/" target="_blank">央视网</a></li>
      <li><a href="http://www.news.cn/" target="_blank">新华网</a></li>
      <li><a href="http://www.people.com.cn/" target="_blank">人民网</a></li>
    </ul>
    <div class="copyright">
<!--      <div id="_ideConac">-->
<!--        <a href="https://bszs.conac.cn/sitename?method=show&id=07FCACA2139AC126E06310291AAC6CFE" target="_blank"><img id="imgConac" vspace="0" hspace="0" border="0" src="https://dcs.conac.cn/image/red.png" data-bd-imgshare-binded="1"></a>-->
<!--      </div>-->
      <div>
        <div>
          <span>增值电信业务经营许可证</span>
          <a style="margin: 0 10px;" href="https://beian.miit.gov.cn/">{{record}} </a>
          <span>著作权保护声明</span>
           </div>
        <div>
          Copyright © 2023 gushiw.com.cn Inc. All Rights Reserved.
          固始县融媒体中心（固始县广播电视台） 版权所有
        </div>
        <div>
          声明：本网部分内容来源于互联网，如有侵权内容请及时
          与网站管理员联系及时删除处理！
        </div>
      </div>
      <div class="tu">
        <div style="display: inline-block;text-align: center;margin-right: 30px;">
          <img src="../../assets/e1.jpg" alt="" style="width: 7rem;"/>
          <div style="">云上固始</div>
        </div>
        <div style="display: inline-block;text-align: center;">
          <img src="../../assets/e2.jpg" alt="" style="width: 7rem;"/>
          <div style="">微信公众号</div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>

export default {
  name: 'footers',
  data () {
    return {
      record:'',
    }
  },
  mounted () {
    var domain = window.location.host;
    console.log(domain);
    if(domain=='gsw.gov.cn'){
      this.record='豫ICP备2024069082号-1';
    }else{
      this.record='豫ICP备2023022635号-3';
    }
  },
  computed: {},
  methods: {},
}
</script>
<style scoped lang="scss">
.tu{
  margin-left: 200px;
}
.footers {
  background: #f7f7f7;

  ul {
    height: 80px;
    display: flex;
    background: #f7f7f7;
    align-items: center;
    justify-content: center;
    border-top: 5px solid #bf0614;
    border-bottom: 1px solid #e6e6e6;
    flex-wrap: wrap;

    li {
      margin-right: 40px;

      &:nth-last-child(1) {
        margin-right: 0;
      }

      a {
        font-size: 20px;
        font-weight: 400;
        color: #808080;

        &:hover {
          color: #bf0614;
        }
      }
    }
  }

  .copyright {
    max-width: 1420px;
    margin: 0 auto;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
    line-height: 28px;
    padding: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@media screen and (max-width: 750px) {
  .footers ul {
    height: 0;
  }
  .footers ul li {
    display: none;
  }
}
</style>
