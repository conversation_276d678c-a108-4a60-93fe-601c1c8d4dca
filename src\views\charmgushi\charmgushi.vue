<template>
  <div>
    <Header/>
    <div class="banner">
      <img src="../../assets/01.jpg" alt=""/>
    </div>
    <div class="crumbs-item">
      <el-breadcrumb class="crumbs" separator-class="el-icon-arrow-right">
        <el-breadcrumb-item
        >
          <router-link
            style="color: #606266; font-weight: initial"
            :to="{ path: '/' }"
          >首页
          </router-link
          >
        </el-breadcrumb-item>
        <el-breadcrumb-item>走进固始</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="main">
      <div class="main-item">
        <div class="main-img-box">
          <img class="main-img" src="../../assets/survey.png" alt=""/>
        </div>
        <div class="survey">
          <!-- <img
            src="../../assets/1.png"
            alt=""
            @click="goAnchor('gushihistory')"
          />
          <img src="../../assets/2.png" alt="" />
          <img src="../../assets/3.png" alt="" />
          <img src="../../assets/8.png" alt="" />
          <img src="../../assets/5.png" alt="" /> -->
          <div class="survey-img" @click="open_alert()">
            <img src="../../assets/VR.png" alt=""/>
            <div>VR Gushi</div>
            <div>VR固始</div>
          </div>
          <div class="survey-img">
            <a
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
              "
              href="https://gushitv.com"
              target="_blank"
            >
              <img src="../../assets/底部_视听.png" alt=""/>
              <div>Audiovisual Gushi</div>
              <div>视听固始</div>
            </a>
          </div>
          <div class="survey-img" @click="jump('gsgaikuang')">
            <img src="../../assets/文化场馆.png" alt=""/>
            <div>Gushi Humanities</div>
            <div>固始人文</div>
          </div>
          <!-- <div class="survey-img" @click="goAnchor('gushihistory')"> -->
          <div class="survey-img" @click="jump('gushihistory')">
            <img src="../../assets/History.png" alt=""/>
            <div>Gushi History</div>
            <div>固始历史</div>
          </div>
          <div class="survey-img" @click="jump('characteristic')">
            <img src="../../assets/形状5.png" alt=""/>
            <div>Gushi Scenery</div>
            <div>固始风光</div>
          </div>
          <div class="survey-img" @click="jump('livelihood')">
            <img src="../../assets/本地生活.png" alt=""/>
            <div>Gushi Life</div>
            <div>固始生活</div>
          </div>
          <div class="survey-img" @click="jump('business')">
            <img src="../../assets/services.png" alt=""/>
            <div style="text-align: center">People's livelihood services</div>
            <div>民生服务</div>
          </div>
        </div>
        <div class="survey-item" id="gsgaikuang">
          <div class="sw-box">
            <div class="swiper-container swiper1">
              <div class="swiper-wrapper">
                <div
                  class="swiper-slide"
                  v-for="(item, index) in overviewImgList"
                  :key="index"
                >
                  <img :src="item" alt=""/>
                </div>
                <!-- <div class="swiper-slide">
                  <img src="../../assets/tu7.jpg" alt="" />
                </div>
                <div class="swiper-slide">
                  <img src="../../assets/tu7.jpg" alt="" />
                </div>
                <div class="swiper-slide">
                  <img src="../../assets/tu7.jpg" alt="" />
                </div> -->
              </div>
              <!-- 如果需要分页器 -->
              <div class="swiper-pagination"></div>
            </div>
          </div>
          <div class="overview">
            <div class="overview-title">固始概况</div>

            <div class="overview-desc">
              {{ overviewText }}
            </div>
            <div class="node-two">
              <div>
                <router-link :to="{ path: `/news/${12}/${18}` }"
                >根亲文化
                </router-link
                >
              </div>
              <div>
                <router-link :to="{ path: `/news/${5}/${18}` }"
                >文化节展
                </router-link
                >
              </div>
              <div>
                <router-link :to="{ path: `/news/${33}/${18}` }">
                  网络中国节
                </router-link
                >
              </div>
              <div>
                <router-link :to="{ path: `/news/${40}/${18}` }"
                >名人轶事
                </router-link
                >
              </div>
              <div>
                <router-link :to="{ path: `/news/${39}/${18}` }"
                >非遗文化
                </router-link
                >
              </div>
            </div>
          </div>
        </div>
        <div class="node">
          <div>
            <router-link :to="{ path: `/news/${12}/${18}` }" class="root_a"
            ><img src="../../assets/根亲文化.png" alt=""
            /></router-link>
          </div>
          <div>
            <router-link :to="{ path: `/news/${5}/${18}` }" class="root_a"
            ><img src="../../assets/文化节展.png" alt=""
            /></router-link>
          </div>
          <div>
            <router-link :to="{ path: `/news/${33}/${18}` }" class="root_a">
              <img src="../../assets/网络中国节.png" alt=""
              /></router-link>
          </div>
          <div>
            <router-link :to="{ path: `/news/${40}/${18}` }" class="root_a"
            ><img src="../../assets/名人轶事.png" alt=""
            /></router-link>
          </div>
          <div>
            <router-link :to="{ path: `/news/${39}/${18}` }" class="root_a">
              <img src="../../assets/非遗文化.png" alt=""/>
            </router-link>
          </div>
        </div>
        <div class="main-img-box">
          <img
            class="main-img"
            id="gushihistory"
            src="../../assets/lishi.png"
            alt=""
          />
        </div>

        <div class="history">
          <div class="history-desc">
            {{ historyText }}
          </div>
          <div class="history-item">
            <el-button style="background: #69d3fa; color: #fff"
            >
              <router-link :to="{ path: `/news/${37}/${17}` }"
              >历史人物
              </router-link
              >
            </el-button>
            <el-button style="background: #53d39c; color: #fff"
            >
              <router-link :to="{ path: `/news/${36}/${17}` }"
              >历史事件
              </router-link
              >
            </el-button
            >
          </div>
          <!-- <div class="history-bj" :style="sliderStyle"> -->
          <div class="history-bj">
            <div class="scroll1" id="scroll1" @click="figure()">
              <img src="../../assets/character.jpg" alt=""/>
            </div>
            <div
              class="scroll1-box"
              id="scroll1-box"
              style="width: 700px; overflow: hidden"
            >
              <div class="swiper-container swiper2" ref="swiper2">
                <div class="swiper-wrapper">
                  <div style="cursor: pointer"
                       class="swiper-slide"
                       v-for="item in historicalFigure"
                       :key="item.id"
                       @click="goDetail(item.id)"
                  >
                    <span style="display: inline-block;">{{ item.news_title }}</span>
                    <span style="margin-top: 20px">{{ item.news_titleshort }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="scroll2" id="scroll2" @click="rightLists()">
              <img src="../../assets/event.jpg" alt=""/>
            </div>
            <div class="scroll3-box">
              <div style="width: 100%; height: 100%; overflow: hidden">
                <div
                  class="item-scroll3"
                  style="width: 600px; overflow: hidden"
                >
                  <div class="swiper-container swiper3" ref="swiper3">
                    <div class="swiper-wrapper">
                      <div
                        class="swiper-slide"
                        v-for="item in historicalEvents"
                        :key="item.id"
                        @click="goDetail(item.id)"
                      >
                        {{ item.news_title }}

                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main-img-box">
          <img
            class="main-img"
            id="characteristic"
            src="../../assets/Scene.png"
            alt=""
          />
        </div>

        <div class="characteristic">
          {{ sceneText }}
        </div>
        <div class="img-bot">
          <el-button class="ly">
            <router-link :to="{ path: `/gushibeauty/${3}` }"
            >旅游景点
            </router-link>
          </el-button
          >
          <el-button class="ts">
            <router-link :to="{ path: `/gushibeauty/${11}` }" style="color: #ffffff">图说固始</router-link>
          </el-button>
          <el-button class="city">
            <router-link :to="{ path: `/gushibeauty/${10}` }" style="color: #ffffff">城市风光</router-link>
          </el-button>
        </div>
        <div class="characteristic-img">
          <div class="img-top">
            <router-link :to="{ path: `/gushibeauty/${3}` }"
            ><img src="../../assets/agricultural.jpg" alt=""/>
            </router-link>
            <router-link :to="{ path: `/gushibeauty/${11}` }">
              <img src="../../assets/scenery.jpg" alt=""/>
            </router-link>
            <router-link :to="{ path: `/gushibeauty/${10}` }">
              <img src="../../assets/culture.jpg" alt=""/>
            </router-link>
          </div>
        </div>
        <div class="main-img-box">
          <img
            class="main-img"
            id="livelihood"
            src="../../assets/Locallife.png"
            alt=""
          />
        </div>
        <div class="livelihood">
          <div class="livelihood-gushi">
            <!-- <img src="../../assets/banner_.jpg" alt="" /> -->
            <div class="hexagon">
              <router-link :to="{ path: '/gushibeauty/7' }"
              ><img
                class="bdsc"
                src="../../assets/bendishangchao.png"
                alt=""
              />
              </router-link>
              <router-link :to="{ path: '/gushibeauty/21' }">
                <img
                  class="jksh"
                  src="../../assets/jiankangshenghuo.png"
                  alt=""
                />
              </router-link>
              <router-link :to="{ path: '/gushibeauty/8' }">
                <img class="gsms" src="../../assets/gushimeishi.png" alt=""/>
              </router-link>
              <img class="gs" src="../../assets/gushi.png" alt=""/>
            </div>
          </div>
          <div class="livelihood-desc">
            <div>
              {{ localLifeText }}
            </div>
          </div>
          <div class="shenghuo-item">
            <div class="shenghuo">
              <el-button class="sc mar"><router-link :to="{ path: '/gushibeauty/7' }" style="color:#FFFFFF;">本地商超</router-link></el-button>
              <el-button class="sh mar"> <router-link :to="{ path: '/gushibeauty/21' }" style="color:#FFFFFF;">健康生活</router-link></el-button>
              <el-button class="ms mar">
                <router-link :to="{ path: '/gushibeauty/8' }" style="color:#FFFFFF;">固始美食</router-link>
              </el-button>
            </div>
          </div>
        </div>
        <div class="main-img-box">
          <img
            class="main-img"
            id="business"
            src="../../assets/Service .png"
            alt=""
          />
        </div>

        <div class="business">
          <div class="business-desc">
            {{ serviceText }}
          </div>
          <div class="business-item">
            <div class="business-btn">
              <el-button style="background: #69d3fa; color: #fff"
              ><router-link :to="{ path: '/gushibeauty/38' }" style="color:#FFFFFF;">养老</router-link>
              </el-button
              >
              <el-button style="background: #53d39c; color: #fff"
              ><router-link :to="{ path: '/gushibeauty/4' }" style="color:#FFFFFF;">招聘</router-link>
              </el-button
              >
              <el-button style="background: #f7acc0; color: #fff"
              ><router-link :to="{ path: '/gushibeauty/35' }" style="color:#FFFFFF;">资讯</router-link>
              </el-button
              >
            </div>
          </div>
          <div class="business-right">
            <div class="yzz">
              <router-link :to="{ path: '/gushibeauty/38' }">
                <div class="care"></div>
              </router-link>
              <router-link :to="{ path: '/gushibeauty/4' }">
                <div class="recruitment"></div>
              </router-link>
              <router-link :to="{ path: '/gushibeauty/35' }">
                <div class="consultation"></div>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <recommend /> -->
    <Footers/>
  </div>
</template>
<script>
import Header from '@/components/header/header.vue'
import Footers from '@/components/footers/footers.vue'
import newsImgList from '@/components/newsImgList/newsImgList.vue'
import recommend from '@/components/recommend/recommend.vue'
import newsList from '@/components/newsList/newsList.vue'
import Swiper from 'swiper'
import { currentPolitics } from '@/api/api.js'

export default {
  name: 'charmgushi',
  data () {
    return {
      sliderStyle: {
        width: '600px',
      },
      historyText: '',
      sceneText: '',
      localLifeText: '',
      serviceText: '',
      overviewText: '',
      overviewImgList: [],
      historicalFigure: [], //历史人物
      historicalEvents: [], //历史事件
      swiper2: '',
      swiper3: '',
    }
  },
  created () {
    this.$nextTick(() => {
      this.getlocal()
    })
  },
  mounted () {
    this.initSwiper2()
    this.$nextTick(function () {
      window.addEventListener('scroll', this.handleScroll)
    })
    this.getHistory()
    this.getScene()
    this.getLocalLifeText()
    this.getService()
    this.getOverview()
    this.getFigure(37)
    this.getRightLists(36)
  },
  computed: {},
  methods: {
    initSwiper1 () {
      new Swiper('.swiper1', {
        loop: true, // 循环模式选项
        autoplay: {
          delay: 3000, //1秒切换一次
        },
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        // 如果需要分页器
        pagination: {
          el: '.swiper-pagination',
        },
      })
    },
    initSwiper2 () {
      let that = this
      this.swiper2 = new Swiper('.swiper2', {
        loop: true, // 循环模式选项
        slidesPerView: 'auto',
        autoplay: {
          delay: 3000, //1秒切换一次
        },
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
      })
      // //鼠标覆盖停止自动切换
      // this.swiper2.el.onmouseover = function () {
      //   that.swiper2.autoplay.stop();
      // };
      // //鼠标离开开始自动切换
      // this.swiper2.el.onmouseout = function () {
      //   that.swiper2.autoplay.start();
      // };
    },
    initSwiper3 () {
      let that = this
      this.swiper3 = new Swiper('.swiper3', {
        loop: true, // 循环模式选项
        slidesPerView: 'auto',
        autoplay: {
          delay: 3000, //1秒切换一次
        },
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
      })
    },
    goAnchor (e) {
      var id = '#' + e
      document.querySelector(id).scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'center',
      })
    },
    // editWidth() {
    //   this.sliderStyle.width = "600px";
    // },
    getlocal () {
      let select = localStorage.getItem('id')
      let elm = document.getElementById(select)
      if (select) {
        elm.scrollIntoView(true)
      }
    },
    getHistory () {
      currentPolitics(17).then((res) => {
        this.historyText = res.data.data.description
      })
    },
    getScene () {
      currentPolitics(16).then((res) => {
        this.sceneText = res.data.data.description
      })
    },
    getLocalLifeText () {
      currentPolitics(30).then((res) => {
        this.localLifeText = res.data.data.description
      })
    },
    getService () {
      currentPolitics(29).then((res) => {
        this.serviceText = res.data.data.description
      })
    },
    getOverview () {
      currentPolitics(55).then((res) => {
        this.overviewText = res.data.data.description
        this.overviewImgList = res.data.data.image
        this.$nextTick(() => {
          this.initSwiper1()
        })
      })
    },
    getFigure (id) {
      currentPolitics(id).then((res) => {
        this.historicalFigure = res.data.data.news
        this.$nextTick(() => {
          this.initSwiper2()
        })
      })
    },
    getRightLists (id) {
      currentPolitics(id).then((res) => {
        this.historicalEvents = res.data.data.news
        this.$nextTick(() => {
          this.initSwiper3()
        })
      })
    },
    // figure() {
    //   this.swiper2.destroy(true);
    //   this.getFigure(37);
    // },
    // rightLists() {
    //   this.swiper2.destroy(true);
    //   this.getFigure(36);
    // },
    figure () {
      let scroll1 = document.getElementById('scroll1-box')
      scroll1.style.width = '700px'
      let scroll2 = document.getElementById('scroll2')
      scroll2.style.right = '-20px'
    },
    rightLists () {
      let scroll1 = document.getElementById('scroll1-box')
      let scroll2 = document.getElementById('scroll2')
      scroll1.style.width = '0'
      scroll2.style.right = '82%'
      console.log(scroll1.style)
    },
    jump (domId) {
      // 当前窗口正中心位置到指定dom位置的距离

      //页面滚动了的距离
      let height =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop

      //指定dom到页面顶端的距离
      let dom = document.getElementById(domId)
      let domHeight = dom.offsetTop - 100

      //滚动距离计算
      var S = Number(height) - Number(domHeight)

      //判断上滚还是下滚
      if (S < 0) {
        //下滚
        S = Math.abs(S) //Math.abs返回一个数的绝对值
        //window.scrollBy：把内容滚动到指定的像素数
        window.scrollBy({
          top: S,
          behavior: 'smooth'
        })
      } else if (S == 0) {
        //不滚
        window.scrollBy({
          top: 0,
          behavior: 'smooth'
        })
      } else {
        //上滚
        S = -S
        window.scrollBy({
          top: S,
          behavior: 'smooth'
        })
      }
    },
    goDetail (id) {
      this.$router.push({
        path: `/newsdetail/${id}`,
      })
    },
    open_alert(){
      alert('功能开发中，敬请期待！');
    },
  },
  destroyed () {
    localStorage.setItem('id', '')
  },
  components: {
    Header,
    Footers,
    newsImgList,
    recommend,
    newsList,
  },
}
</script>
<style>
.swiper-container-horizontal
> .swiper-pagination-bullets
.swiper-pagination-bullet {
  background: #979fb0;
  opacity: 1;
}
</style>
<style scoped lang="scss">
.root_a {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.banner {
  // height: 360px;
  width: 100%;

  img {
    // height: 360px;
    width: 100%;
    min-height: 140px;
    object-fit: cover;
    display: block;
  }
}

.crumbs-item {
  background: #f7f7f7;
  border-bottom: 1px solid #cccccc;

  .crumbs {
    height: 50px;
    max-width: 1420px;
    margin: 0 auto;
    line-height: 50px;
    font-size: 16px;
    font-weight: 400;
    color: #808080;
  }
}

.main {
  background: #fff;
  padding: 60px 0 80px;

  .main-item {
    max-width: 1420px;
    margin: 0 auto;

    .main-img {
      max-width: 1192px;
      height: 111px;
      width: 100%;
      margin: 0 auto;
      display: block;
      margin-bottom: 60px;
      object-fit: cover;
    }

    .survey {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;

      .survey-img {
        margin-bottom: 20px;
        width: 180px;
        height: 140px;
        background: #f7f7f7;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-right: 20px;

        &:hover img {
          transform: rotateY(180deg);
        }

        cursor: pointer;

        img {
          display: block;
          transition: 0.5s;
        }

        &:nth-last-child(1) {
          margin-right: 0;
        }

        div {
          &:nth-child(2) {
            font-size: 12px;
            color: #808080;
            margin: 8px 0 0;
          }

          &:nth-child(3) {
            font-size: 24px;
            color: #333333;
          }
        }
      }
    }

    .survey-item {
      display: flex;
      margin-bottom: 60px;

      .swiper1 {
        width: 700px;
        height: 420px;
        margin-right: 60px;

        ::v-deep .swiper-pagination-bullet-active {
          background-color: #fff;
        }

        .swiper-pagination {
          height: 50px;
          line-height: 50px;
          background: linear-gradient(
              to bottom,
              rgba(0, 0, 0, 0),
              rgba(0, 0, 0, 0.8)
          );
          bottom: 0px;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .overview {
        .overview-title {
          display: flex;
          font-size: 32px;
          font-weight: 400;
          color: #333333;
          line-height: 33px;
          margin-top: 10px;

          &::before {
            content: "";
            display: block;
            width: 11px;
            height: 37px;
            background: linear-gradient(-90deg, #bf0614, #ff4c4c);
            margin-right: 11px;
          }
        }

        .overview-desc {
          margin-top: 40px;
          font-size: 20px;
          font-weight: 400;
          color: #333333;
          line-height: 45px;
          overflow: hidden; //超出的文本隐藏
          display: -webkit-box;
          -webkit-line-clamp: 7; // 超出多少行
          -webkit-box-orient: vertical;
          text-indent: 2em;
          text-align: justify;
          text-justify: distribute;
        }
      }
    }

    .node {
      display: flex;
      justify-content: center;
      margin-bottom: 40px;
      flex-wrap: wrap;

      div {
        width: 268px;
        height: 120px;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        background-image: url("../../assets/半圆.png");
        background-size: 268px 120px;
        opacity: 0.75;
        margin-bottom: 20px;
        transition: 0.5s;

        &:hover {
          transform: translate(-10px, -10px);
          box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2),
          0 6px 20px 0 rgba(0, 0, 0, 0.19);
        }

        img {
          display: block;
        }

        &:nth-last-child(1) {
          margin-right: 0;
        }

        &:nth-child(1) {
          background-color: #69d3fa;

          img {
            width: 160px;
            height: 36px;
          }
        }

        &:nth-child(2) {
          background-color: #f7acc0;

          img {
            width: 162px;
            height: 34px;
          }
        }

        &:nth-child(3) {
          background-color: #df3a3a;

          img {
            width: 197px;
            height: 34px;
          }
        }

        &:nth-child(4) {
          background-color: #53d39c;

          img {
            width: 158px;
            height: 36px;
          }
        }

        &:nth-child(5) {
          background-color: #ff8603;

          img {
            width: 161px;
            height: 35px;
          }
        }
      }
    }

    .node-two {
      display: none;
      justify-content: center;
      margin-bottom: 40px;
      flex-wrap: wrap;

      div {
        width: 268px;
        height: 120px;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        background-image: url("../../assets/半圆.png");
        background-size: 100% 100%;
        opacity: 0.75;
        margin-bottom: 20px;
        transition: 0.5s;

        a {
          color: #fff;
        }

        &:nth-last-child(1) {
          margin-right: 0;
        }

        &:nth-child(1) {
          background-color: #69d3fa;
          width: 26%;
          height: 10vw;
        }

        &:nth-child(2) {
          background-color: #f7acc0;
          width: 26%;
          height: 10vw;
        }

        &:nth-child(3) {
          background-color: #df3a3a;
          width: 26%;
          height: 10vw;
        }

        &:nth-child(4) {
          background-color: #53d39c;
          width: 26%;
          height: 10vw;
        }

        &:nth-child(5) {
          background-color: #ff8603;
          width: 26%;
          height: 10vw;
        }
      }
    }

    .history {
      display: flex;
      margin-bottom: 60px;
      // flex-wrap: wrap;
      .history-desc {
        text-indent: 2em;
        margin-right: 53px;
        width: 668px;
        font-size: 20px;
        font-weight: 400;
        color: #333333;
        line-height: 45px;
        margin-top: 25px;
        overflow: hidden; //超出的文本隐藏
        display: -webkit-box;
        -webkit-line-clamp: 8; // 超出多少行
        -webkit-box-orient: vertical;
        text-align: justify;
        text-justify: distribute;
      }

      .history-bj {
        // padding: 40px 50px;
        width: 700px;
        height: 383px;
        position: relative;
        flex-shrink: 0;
        // background: url("../../assets/history-bj.jpg") no-repeat;
        background-size: 100% 100%;
        transition: 2s;
        display: flex;

        .scroll1-box {
          padding: 40px 50px;
          background: url("../../assets/history-bj.jpg") no-repeat;
          z-index: 3;
          transition: 2s;
        }

        .scroll1 {
          width: 77px;
          height: 457px;
          position: absolute;
          top: -37px;
          left: -20px;
          z-index: 99;
          background-image: url("../../assets/scroll.png");
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          img {
            width: 50px;
            height: 220px;
          }
        }

        .scroll2 {
          width: 77px;
          height: 457px;
          position: absolute;
          top: -37px;
          right: -20px;
          z-index: 99;
          background-image: url("../../assets/scroll.png");
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: 2s;

          img {
            width: 50px;
            height: 220px;
          }
        }

        .swiper2 {
          height: 100%;
          transition: 1s;
          width: 600px;

          .swiper-slide {
            writing-mode: vertical-rl;
            line-height: 60px;
            width: 60px;
            font-size: 20px;
            font-weight: 400;
            color: #333333;
            border-right: 1px solid #a2a2a2;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; // 默认不换行；
          }
        }

        .scroll3-box {
          padding: 40px 50px;
          max-width: 700px;
          width: 100%;
          height: 383px;
          position: absolute;
          background: url("../../assets/history-bj.jpg") no-repeat;
          background-size: 100% 100%;
          top: 0;
          left: 0;
          overflow: hidden;

          .item-scroll3 {
            height: 100%;

            .swiper3 {
              height: 100%;

              .swiper-slide {
                writing-mode: vertical-rl;
                line-height: 60px;
                width: 60px;
                font-size: 20px;
                font-weight: 400;
                color: #333333;
                border-right: 1px solid #a2a2a2;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; // 默认不换行；
              }
            }
          }
        }
      }

      .history-item {
        display: none;
      }
    }

    .img-bot {
      display: none;
    }

    .characteristic {
      font-size: 20px;
      font-weight: 400;
      color: #333333;
      line-height: 45px;
      text-indent: 2em;
      text-align: justify;
      text-justify: distribute;
    }

    .characteristic-img {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 45px 0 40px;

      img {
        width: 460px;
        height: 160px;
        margin-bottom: 20px;
        transition: 0.3s;
        cursor: pointer;

        &:hover {
          box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .livelihood {
      display: flex;
      margin-bottom: 60px;

      .shenghuo-item {
        display: none;
      }

      .shenghuo {
        display: flex;
        justify-content: center;
        // .mar {
        //   margin-bottom: 30px;
        // }
        .sc {
          background: #69d3fa;
          color: #fff;
        }

        .sh {
          background: #53d39c;
          color: #fff;
        }

        .ms {
          background: #f7acc0;
          color: #fff;

          a {
            color: #fff;
          }
        }
      }

      .livelihood-gushi {
        width: 445px;
        margin-right: 55px;

        .hexagon {
          width: 380px;
          height: 380px;
          position: relative;
          margin-top: 40px;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            display: block;
            cursor: pointer;
            transition: 0.3s;

            &:hover {
              filter: drop-shadow(5px 5px 5px rgba(0, 0, 0, 0.2)) !important;
            }
          }

          .bdsc {
            width: 238px;
            height: 248px;
            position: absolute;
            top: 0;
            left: 0;
          }

          .jksh {
            width: 171px;
            position: absolute;
            height: 305px;
            top: 0;
            right: 0;
          }

          .gsms {
            width: 311px;
            position: absolute;
            height: 167px;
            bottom: 0;
            left: 19px;
          }

          .gs {
            width: 80px;
            height: 86px;
          }
        }
      }

      .livelihood-desc {
        padding: 50px 34px 50px 85px;
        width: 920px;
        position: relative;
        text-align: justify;
        text-justify: distribute;
        div {
          font-size: 20px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          line-height: 45px;
          overflow: hidden; //超出的文本隐藏
          display: -webkit-box;
          -webkit-line-clamp: 8; // 超出多少行
          -webkit-box-orient: vertical;
          text-indent: 2em;
        }

        background: #f7f7f7;

        &::before {
          content: "";
          display: block;
          width: 0;
          height: 0;
          border-top: 115px solid #fff;
          border-left: 30px solid #fff;
          border-right: 30px solid transparent;
          border-bottom: 115px solid transparent;
          position: absolute;
          top: 0;
          left: 0;
        }

        &::after {
          content: "";
          display: block;
          width: 0;
          height: 0;
          border-top: 115px solid transparent;
          border-left: 25px solid #fff;
          border-right: 25px solid transparent;
          border-bottom: 115px solid #fff;
          position: absolute;
          bottom: 0;
          left: 0;
        }
      }
    }

    .business {
      font-size: 20px;
      font-weight: 400;
      color: #333333;
      line-height: 45px;
      display: flex;
      flex-wrap: wrap;

      .business-desc {
        width: 800px;
        height: 425px;
        margin-right: 140px;
        text-indent: 2em;
        overflow: hidden; //超出的文本隐藏
        display: -webkit-box;
        -webkit-line-clamp: 9; // 超出多少行
        -webkit-box-orient: vertical;
        text-align: justify; text-justify: distribute;
      }

      .business-item {
        display: none;
        width: 100%;

        .business-btn {
          display: flex;
          justify-content: center;
        }
      }

      .business-right {
        width: 368px;
        height: 401px;
        background-image: url("../../assets/yzz.png");
        background-size: 368px 401px;
        background-repeat: no-repeat;
        position: relative;

        .care {
          top: 15px;
          left: 16px;
          position: absolute;
          width: 128px;
          height: 128px;
          border-radius: 128px;
          cursor: pointer;
        }

        .recruitment {
          right: 13px;
          top: 138px;
          position: absolute;
          width: 128px;
          height: 128px;
          border-radius: 128px;
          cursor: pointer;
        }

        .consultation {
          bottom: 15px;
          left: 16px;
          position: absolute;
          width: 128px;
          height: 128px;
          border-radius: 128px;
          cursor: pointer;
        }
      }
    }
  }
}

.jctj {
  background: #f7f7f7;
  padding-top: 57px;
  padding-bottom: 132px;

  .jctj-item {
    width: 1420px;
    margin: 0 auto;

    .jctj-img {
      height: 63px;
      display: block;
      margin: 0 auto 59px;
    }

    .culture {
      display: flex;
      justify-content: space-between;
    }
  }
}

@media screen and (max-width: 1480px) {
  .crumbs-item .crumbs {
    padding: 0 30px;
  }
  .main .main-item .survey {
    padding: 0 30px;
  }
  .main .main-item .survey-item {
    margin: 0 auto 60px;
  }
}

@media screen and (max-width: 1420px) {
  // .main .main-item .history {
  //   display: block;
  // }
  .main .main-item .history .history-desc {
    width: 100%;
  }
  .main {
    padding: 60px 30px 80px;
  }
  .main .main-item .history .history-bj {
    margin: 60px auto 0;
  }
}

@media screen and (max-width: 1120px) {
  .main .main-item .survey-item {
    display: block;
  }
  .main .main-item .history {
    display: block;
  }
  .main .main-item .survey-item .swiper1 {
    margin: 0 auto 40px;
  }
  .main {
    padding: 60px 30px 80px;
  }
  .main .main-item .livelihood {
    display: block;
  }
  .main .main-item .livelihood .livelihood-gushi {
    margin: 0 auto 40px;
  }
  .main .main-item .livelihood .livelihood-desc {
    width: auto;
  }
  .main .main-item .business .business-desc {
    margin-right: 0;
    width: 100%;
    height: auto;
  }
  .main .main-item .business .business-right {
    margin: 0 auto;
  }
}

@media screen and (max-width: 750px) {
  .main .main-item .survey-item .overview .overview-title {
    font-size: 20px;
    margin-bottom: 10px;

    &::before {
      height: 25px;
    }
  }
  .main .main-item .characteristic-img {
    padding: 0 30px;
  }
  .main .main-item .history {
    padding: 0 30px;
  }
  .main .main-item .survey-item .overview {
    padding: 0 30px;
  }
  .main .main-item .main-img-box {
    padding: 0 30px;
  }
  // .main .main-item .history .history-bj {
  //   display: none;
  // }
  .main {
    padding: 30px 0 60px;
  }
  .main .main-item .node-two {
    display: flex;
    margin: 20px 0 0;
  }
  .main .main-item .survey {
    padding: 0 30px;
    margin-bottom: 0;
  }
  .main .main-item .survey .survey-img {
    width: 22%;
    height: auto;
    padding: 10px;
    margin-right: 5px;
    border-radius: 8px;
  }
  .main .main-item .survey .survey-img img {
    width: 5vw;
  }
  .main .main-item .survey .survey-img div {
    &:nth-child(2) {
      text-align: center;
      display: none;
    }

    &:nth-child(3) {
      font-size: 14px;
      margin-top: 5px;
    }
  }
  .main .main-item .survey-item .overview .overview-desc {
    margin-top: 0;
    font-size: 16px;
    line-height: 1.5;
  }
  .main .main-item .history .history-desc {
    margin-top: 20px;
    font-size: 16px;
    line-height: 1.5;
  }
  .main .main-item .characteristic {
    margin-top: 20px;
    font-size: 16px;
    line-height: 1.5;
    padding: 0 30px;
  }
  .main .main-item .livelihood .livelihood-desc {
    padding: 0;
    background: #fff;

    &::before {
      content: none;
    }

    &::after {
      content: none;
    }
  }
  .main .main-item .livelihood .livelihood-desc div {
    margin-top: 20px;
    font-size: 16px;
    line-height: 1.5;
  }
  .main .main-item .history,
  .main .main-item .livelihood {
    margin-bottom: 35px;
    padding: 0 30px;
  }
  .main .main-item .business {
    padding: 0 30px;
  }
  .main .main-item .survey-item {
    margin: 0 auto 10px;
  }
  .main .main-item .node {
    margin-bottom: 20px;
    display: none;
  }
  .main .main-item .node div {
    width: 26%;
    height: 10vw;

    &:nth-child(1) {
      img {
        width: 15vw;
        height: 4vw;
      }
    }

    &:nth-child(2) {
      img {
        width: 15vw;
        height: 4vw;
      }
    }

    &:nth-child(3) {
      img {
        width: 15vw;
        height: 4vw;
      }
    }

    &:nth-child(4) {
      img {
        width: 15vw;
        height: 4vw;
      }
    }

    &:nth-child(5) {
      img {
        width: 15vw;
        height: 4vw;
      }
    }
  }
  .main .main-item .characteristic-img {
    .img-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    a {
      display: inline-block;
      width: 33%;
      height: auto;

      img {
        max-width: 100%;
      }
    }
  }
  .main .main-item .business .business-desc {
    // margin-top: 20px;
    font-size: 16px;
    line-height: 1.5;
  }
  .main .main-item .survey-item .swiper1 {
    width: 100%;
    height: 60vw;
  }
  .main .main-item .survey-item .swiper1 .swiper-slide {
    height: auto;
  }
  .main .main-item .survey-item .swiper1 .swiper-pagination {
    background: none;
  }
  .main .main-item .main-img {
    height: 65px;
    margin-bottom: 25px;
  }
  .main .main-item .characteristic-img img {
    max-width: 33%;
    width: 100%;
    height: auto;
    display: block;
  }
  .main .main-item .node div:nth-last-child(1) {
    margin-right: 20px;
  }
  .main .main-item .history .history-bj {
    width: 100%;
  }
}

@media screen and (max-width: 670px) {
  .main .main-item .history .history-bj {
    display: none;
  }
  .main .main-item .history .history-item {
    display: block;
    text-align: center;
    margin: 20px 0 25px;

    a {
      color: #fff;
    }
  }
  .main .main-item .livelihood .livelihood-gushi {
    display: none;
  }
  .main .main-item .livelihood .shenghuo-item {
    display: block;
    margin-top: 20px;
  }
  .main .main-item .business .business-right {
    display: none;
  }
  .main .main-item .business .business-item {
    display: block;
    margin-top: 20px;
  }
  .main .main-item .characteristic-img .img-top {
    display: none;
  }
  .main .main-item .img-bot {
    display: flex;
    justify-content: center;
    width: 100%;
    padding: 0 30px;
    margin-top: 20px;

    .ly {
      background: #69d3fa;
      color: #fff;

      a {
        color: #fff;
      }
    }

    .ts {
      background: #53d39c;
      color: #fff;
    }

    .city {
      background: #f7acc0;
      color: #fff;
    }
  }
  .main .main-item .characteristic-img {
    margin: 0 0 35px;
  }
}

@media screen and (max-width: 530px) {
  .main .main-item .survey .survey-img {
    &:nth-last-child(1) {
      margin-right: 20px;
    }
  }
}

@media screen and (max-width: 450px) {
  // .main .main-item .livelihood .livelihood-gushi {
  //   display: none;
  // }
  // .main .main-item .livelihood .shenghuo-item {
  //   display: block;
  // }
  // .main .main-item .business .business-right {
  //   display: none;
  // }
  // .main .main-item .business .business-item {
  //   display: block;
  // }
  .main .main-item .survey .survey-img div {
    &:nth-child(3) {
      font-size: 12px;
      margin-top: 5px;
      text-align: center;
    }
  }
  .main .main-item .node-two div a {
    font-size: 14px;
  }
  // .main .main-item .characteristic-img {
  //   margin: 0 0 35px;
  // }
  // .main .main-item .characteristic-img .img-top {
  //   display: none;
  // }
  // .main .main-item .img-bot {
  //   display: flex;
  //   justify-content: center;
  //   width: 100%;
  //   .ly {
  //     background: #69d3fa;
  //     color: #fff;
  //     a {
  //       color: #fff;
  //     }
  //   }
  //   .ts {
  //     background: #53d39c;
  //     color: #fff;
  //   }
  //   .city {
  //     background: #f7acc0;
  //     color: #fff;
  //   }
  // }
}
</style>
