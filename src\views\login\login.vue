<template>
  <div>
    <Header />
    <div class="content">
      <div class="item">
        <div class="title">
          <div>账号登录</div>
          <div>没有账号，<span @click="goRegister()">立即注册</span></div>
        </div>
        <div class="login">
          <div class="l">
            <!-- <p>Welcome</p>
            <p>固始电视台</p> -->
            <img src="../../assets/welcone.png" alt="" />
          </div>
          <div class="r">
            <el-input
              prefix-icon="el-icon-phone"
              class="inp"
              maxLength="11"
              v-model="phone"
              oninput="value=value.replace(/[^\d.]/g,'')"
              placeholder="请输入手机号"
            >
            </el-input>
            <form>
              <el-input
                prefix-icon="el-icon-s-custom"
                v-model="password"
                placeholder="请输入密码"
                show-password
                type="password"
              ></el-input>
            </form>
            <div class="forget">
              <el-button class="btn" type="primary" @click="getLogin()"
                >登录</el-button
              >
              <router-link :to="{ name: 'password' }"
                ><div>忘记密码</div></router-link
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <Footers />
  </div>
</template>
<script>
import Header from "@/components/header/header.vue";
import Footers from "@/components/footers/footers.vue";
import { login } from "@/api/api.js";
import axios from "axios";
export default {
  data() {
    return {
      phone: "",
      password: "",
    };
  },
  mounted() {},
  computed: {},
  methods: {
    goRegister() {
      this.$router.push({ name: "register" });
    },
    getLogin() {
      const data = {
        account: this.phone,
        password: this.password,
      };

      login(data).then((res) => {
        // console.log(res);
        if (res.data.code == 200) {
          this.$message({
            message: res.data.msg,
            type: "success",
          });
          this.$store.commit("setToken", res.data.data.userinfo.token);
          this.$store.commit("setUserName", res.data.data.userinfo.username);
          this.$store.commit("setUserMobile", res.data.data.userinfo.mobile);
          this.$store.commit("setUserEmail", res.data.data.userinfo.email);
          this.$router.push({ name: "index" });
        } else {
          this.$message({
            message: res.data.msg,
            type: "error",
          });
        }
      });
    },
  },
  components: {
    Header,
    Footers,
  },
};
</script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
</style>

<style scoped lang="scss">
.content {
  padding: 100px 0 100px;
  background: url("../../assets/loginbj.png");
  .item {
    max-width: 800px;
    margin: 0 auto;
    .title {
      width: 100%;
      font-size: 20px;
      font-weight: bold;
      color: #0f4b9d;
      background-color: #fff;
      padding: 20px 40px;
      display: flex;
      justify-content: space-between;
      div {
        &:nth-child(2) {
          font-size: 12px;
          color: #808080;
        }
        span {
          color: #0f4b9d;
          cursor: pointer;
        }
      }
    }
    .login {
      display: flex;
      padding: 20px 40px;
      background-color: #fff;
      justify-content: center;
      .l {
        width: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 80%;
        }
      }
      .r {
        width: 50%;
        .inp {
          margin: 20px 0;
        }
        .btn {
          width: 70%;
          margin: 20px 0 60px;
        }
        .forget {
          display: flex;
          justify-content: space-between;
          div {
            font-size: 12px;
            color: #808080;
            font-weight: bold;
            margin-top: 42px;
            cursor: pointer;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 750px) {
  .content {
    padding: 100px 30px 100px;
  }
}
@media screen and (max-width: 450px) {
  .content .item .login .l {
    display: none;
  }
  .content .item .login .r .forget {
    display: flex;
    align-items: end;
  }
  .content .item .login .r .forget div {
    margin-top: 0;
  }
  .content .item .login .r {
    width: 85%;
    margin: 20px 0 0px;
  }
  .content .item .login {
    padding: 0px 0px 40px;
  }
  .content .item .login .r .btn {
    margin: 20px 0 0px;
  }
  .content .item .login .r .inp {
    margin: 0 0 20px;
  }
}
</style>
