<template>
  <div class="header">
    <div class="pro">
      <div class="nav">
        <div>
          <router-link :to="{ path: '/' }"
          ><img class="logo-img" src="../../assets/logo.png" alt=""
          /></router-link>
        </div>
        <div class="item">
          <ul>
            <li>
              <router-link :to="{ path: '/charmgushi' }">走进固始</router-link>
            </li>
            <li>
              <router-link :to="{ path: `/news/${countSzyw.id}/${14}` }"
              >时政要闻
              </router-link
              >
            </li>
            <li>
              <router-link :to="{ path: `/newsinfo/${15}` }"
              >政策信息
              </router-link
              >
            </li>
            <li>
              <router-link :to="{ path: `/news/${countZtzl.id}/${13}` }"
              >专题专栏
              </router-link
              >
            </li>
            <li>
              <router-link :to="{ path: `/newsinfo/${9}` }"
              >公示公告
              </router-link
              >
            </li>
            <li>
              <router-link :to="{ path: `/gushiys/${6}` }"
              >固始营商
              </router-link
              >
            </li>
          </ul>
          <div class="icon">
            <img src="../../assets/搜索.png" alt="" @click="goSearch()"/>
            <div class="flex">
              <img class="img" src="../../assets/phone.png" alt=""/>
              <div>
                <img src="../../assets/e1.jpg" alt=""/>
              </div>
            </div>
            <div class="flex1">
              <img class="img" src="../../assets/weixin.png" alt=""/>
              <div>
                <img src="../../assets/e2.jpg" alt=""/>
              </div>
            </div>
<!--            <img @click="goLogin()" src="../../assets/user.png" alt=""/>-->
          </div>
        </div>
        <div class="none">
          <div class="el-icon-more" @click="dianji()"></div>
          <!-- <div class="el-icon-more" @click="drawer = true"></div> -->
          <div class="el-icon-close" @click="dianji1()"></div>
        </div>
        <div class="nav-right1">
          <div class="nav-title">
            <ul>
              <li>
                <router-link :to="{ path: '/' }"> 网站首页</router-link>
              </li>
              <li>
                <router-link :to="{ path: '/charmgushi' }">
                  走进固始
                </router-link
                >
              </li>
              <li class="szyw-sz" id="szyw_sz1">
                <div class="nav-title-icon">
                  <router-link :to="{ path: `/news/${countSzyw.id}/${14}` }"
                  >时政要闻
                  </router-link
                  >
                  <div
                    :class="
                      flag1 == false
                        ? ' el-icon-arrow-right sz'
                        : 'el-icon-arrow-down'
                    "
                    @click.stop="szyw_sz()"
                  ></div>
                </div>
                <ul class="two-item two-item1">
                  <li
                    v-for="item in szywTwoList"
                    :key="item.id"
                    @click="goNewsList(item.id, item.pid)"
                  >
                    {{ item.name }}
                  </li>
                </ul>
              </li>
              <li>
                <router-link :to="{ path: `/newsinfo/${15}` }"
                >政策信息
                </router-link
                >
              </li>
              <li>
                <router-link :to="{ path: `/news/${countZtzl.id}/${13}` }"
                >专题专栏
                </router-link
                >
              </li>
              <li>
                <router-link :to="{ path: `/newsinfo/${9}` }"
                >公示公告
                </router-link
                >
              </li>
              <li class="szyw-sz" id="szyw_sz2">
                <div class="nav-title-icon">
                  <router-link :to="{ path: `/gushiys/${6}` }"
                  >固始营商
                  </router-link
                  >
                  <div
                    :class="
                      flag == false
                        ? ' el-icon-arrow-right ys'
                        : 'el-icon-arrow-down'
                    "
                    @click.stop="ysgs_ys()"
                  ></div>
                </div>
                <ul class="two-item two-item2">
                  <li
                    v-for="item in gsysTwoList"
                    :key="item.id"
                    @click="goNewsList(item.id, item.pid)"
                  >
                    {{ item.name }}
                  </li>
                </ul>
              </li>
              <li class="l-z" v-if="registerText == true">
                <div class="login-item" @click="goLogin()">登录</div>
                <div class="zhuce" @click="goZhuce()">注册</div>
              </li>
              <li
                class="liuyan"
                v-if="registerText == false"
                @click="goLiuyan()"
              >
                留言
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="drawer">
      <ul>
        <li v-if="registerText == true">
          <div class="login-item" @click="goLogin()">登录</div>
          <div class="zhuce" @click="goZhuce()">注册</div>
        </li>
        <li class="liuyan" v-if="registerText == false" @click="goLiuyan()">
          留言
        </li>
        <li>
          <router-link :to="{ path: '/charmgushi' }"> 走进固始</router-link>
        </li>
        <li>
          <router-link :to="{ path: `/news/${countSzyw.id}/${14}` }"
            >时政要闻</router-link
          >
        </li>
        <li>政策信息</li>
        <li>
          <router-link :to="{ path: `/news/${countZtzl.id}/${13}` }"
            >专题专栏</router-link
          >
        </li>
        <li>公示公告</li>
        <li>
          <router-link :to="{ path: `/news/${countZtzl.id}/${6}` }"
            >固始营商</router-link
          >
        </li>
      </ul>
    </div> -->
  </div>
</template>
<script>
import { navigation } from '@/api/api.js'

export default {
  data () {
    return {
      countSzyw: 0,
      countZtzl: 0,
      autofocus: '',
      drawer: false,
      registerText: true,
      szywTwoList: [],
      gsysTwoList: [],
      flag: false,
      flag1: false,
    }
  },
  mounted () {
    this.getNavigation()
    this.getSzyw()
    this.getFeature()
    this.getGsys()
    const token = this.$store.getters.getToken
    if (token !== '') {
      this.registerText = false
    } else {
      this.registerText = true
    }
  },
  computed: {},
  methods: {
    getNavigation () {
      navigation().then((res) => {
        // console.log(res);
      })
    },
    getSzyw () {
      navigation(14).then((res) => {
        this.szywTwoList = res.data.data
        this.countSzyw = res.data.data[0]
      })
    },
    //固始营商
    getGsys () {
      navigation(6).then((res) => {
        console.log(res)
        this.gsysTwoList = res.data.data
      })
    },
    //专题专栏
    getFeature () {
      navigation(13).then((res) => {
        this.countZtzl = res.data.data[0]
      })
    },
    dianji () {
      let btn1 = document.getElementsByClassName('el-icon-close')
      let btn2 = document.getElementsByClassName('el-icon-more')
      let ul1 = document.getElementsByClassName('nav-right1')
      btn1[0].style.display = 'block'
      btn2[0].style.display = 'none'
      ul1[0].style.display = 'block'
    },
    dianji1 () {
      let btn1 = document.getElementsByClassName('el-icon-close')
      let btn2 = document.getElementsByClassName('el-icon-more')
      let ul1 = document.getElementsByClassName('nav-right1')
      btn1[0].style.display = 'none'
      btn2[0].style.display = 'block'
      ul1[0].style.display = 'none'
    },
    getSeach () {
      let inp = document.getElementById('sousuo')
      inp.style.width = '200px'
      inp.style.right = '30px'
      inp.style.padding = '0 15px'
    },
    aa () {
      let inp = document.getElementById('sousuo')
      inp.style.width = '0'
      inp.style.right = '0'
      inp.style.padding = '0'
    },
    goSearch () {
      this.$router.push({ path: '/search' })
    },
    goLogin () {
      const token = this.$store.getters.getToken
      if (token !== '') {
        this.$router.push({
          name: 'user',
        })
      } else {
        this.$router.push({
          name: 'login',
        })
      }
    },
    goZhuce () {
      this.$router.push({
        name: 'register',
      })
    },
    goLiuyan () {
      this.$router.push({
        name: 'user',
      })
    },
    szyw_sz () {
      // let szyw_sz11 = document.getElementsByClassName("two-item1")[0];
      // let sz = document.getElementsByClassName("sz")[0];
      // sz.style.transform = "rotate(90deg)";
      // szyw_sz11.style.height = "max-content";
      this.$('.two-item1').toggle(300)
      this.$('.two-item2').hide(300)
      // this.$(".sz").click(function () {
      //   this.$(".two-item1").fadeToggle();
      // });
      this.flag1 = !this.flag1
    },
    ysgs_ys () {
      this.$('.two-item2').toggle(300)
      this.$('.two-item1').hide(300)
      this.flag = !this.flag
    },
    //固始人文
    goNewsList (id, pid) {
      this.$router.push({
        path: `/news/${id}/${pid}`,
      })
    },
  },
}
</script>
<style>
.el-drawer__open .el-drawer.rtl {
  width: 50% !important;
  padding: 30px;
}
</style>

<style scoped lang="scss">
.login-item {
  cursor: pointer;
}

.zhuce {
  cursor: pointer;
}

.liuyan {
  cursor: pointer;
}

.header {
  background: #ffffff;
  height: 70px;
  position: relative;

  .drawer {
    background: #fff;
    text-align: left;
    font-size: 24px;
    font-weight: 400;
    color: #808080;

    a {
      font-size: 24px;
      font-weight: 400;
      color: #808080;
    }

    .search {
      display: flex;
      align-items: center;
      margin-top: 8px;

      input {
        height: 26px;

        margin-right: 10px;
      }
    }
  }

  .pro {
    position: fixed;
    background: #ffffff;
    width: 100%;
    top: 0;
    left: 0;
    height: 70px;
    box-shadow: 0px 0px 10px 0px rgba(4, 14, 33, 0.3);
    z-index: 999;
  }

  .nav {
    display: flex;
    margin: 0 auto;
    height: 70px;
    max-width: 1420px;
    justify-content: space-between;
    align-items: center;

    .el-icon-close {
      display: none;
      font-size: 30px;
    }

    .el-icon-more {
      font-size: 30px;
      display: none;
    }

    // .nav-right1 {
    //   display: none;
    //   position: absolute;
    //   // margin-top: 20px;
    //   z-index: 9999;
    //   height: 100vh;
    //   width: 50%;
    //   right: 0;
    //   top: 70px;
    //   background: #eee;
    //   text-align: left;
    //   padding: 10px 20px 0;
    //   font-size: 16px;
    //   font-weight: 400;
    //   color: #666;
    //   a {
    //     font-size: 24px;
    //     font-weight: 400;
    //     color: #808080;
    //   }
    //   .search {
    //     width: 100%;
    //     height: 30px;
    //     position: relative;
    //     input {
    //       width: 100%;
    //       height: 30px;
    //       background: #e6e6e6;
    //       border-radius: 15px;
    //       border: none;
    //       outline: none;
    //       font-size: 16px;
    //       font-weight: 400;
    //       color: #808080;
    //       padding-left: 14px;
    //       box-sizing: border-box;
    //     }
    //     i {
    //       width: 16px;
    //       height: 16px;
    //       position: absolute;
    //       top: 7px;
    //       right: 12px;
    //       color: #808080;
    //     }
    //   }
    // }
    .nav-right1 {
      display: none;
      position: absolute;
      // margin-top: 20px;
      z-index: 9999;
      height: 100vh;
      width: 50%;
      right: 0;
      top: 70px;
      background: #eee;
      text-align: left;
      padding: 10px 20px 0;
      font-size: 16px;
      font-weight: 400;
      color: #666;
      // line-height: 1.5;
      .wza {
        // border-top: 1px #ddd solid;
        padding: 5px 0;
      }

      .login-item {
        border-top: none;
      }

      .zhuce {
        border-top: 1px #ddd solid;
      }

      .liuyan {
        border-top: 1px #ddd solid;
      }

      .nav-title {
        a {
          font-size: 16px;
          font-weight: 400;
          color: #666;
        }

        ul {
          li {
            // padding: 5px 0;
            // line-height: 1.5;
            border-top: 1px #ddd solid;
            height: 40px;
            line-height: 40px;
            overflow: hidden;

            &:nth-child(1) {
              border-top: none;
            }

            &:nth-last-child(1) {
              border-bottom: 1px #ddd solid;
            }
          }

          .l-z {
            height: auto;
          }

          #szyw_sz1 {
            height: max-content;
          }

          #szyw_sz2 {
            height: max-content;
          }

          .nav-title-icon {
            display: flex;
            justify-content: space-between;
            align-items: center;

            div {
              padding: 0 8px;
            }
          }

          .two-item {
            padding-left: 15px;
            box-sizing: border-box;

            li {
              color: #999;

              &:nth-last-child(1) {
                padding: 5px 0 0 0;
              }
            }
          }

          .two-item1 {
            display: none;
          }

          .two-item2 {
            display: none;
          }
        }
      }

      .search {
        width: 100%;
        height: 30px;
        position: relative;

        input {
          width: 100%;
          height: 30px;
          background: #e6e6e6;
          border-radius: 15px;
          border: none;
          outline: none;
          font-size: 16px;
          font-weight: 400;
          color: #808080;
          padding: 0 30px 0 14px;
          box-sizing: border-box;
        }

        img {
          width: 16px;
          height: 16px;
          position: absolute;
          top: 11px;
          right: 12px;
          cursor: pointer;
          // color: #808080;
        }
      }
    }

    .el-icon-close {
      display: none;
    }

    .el-icon-more {
      display: none;
    }

    .logo-img {
      width: 132px;
      height: 56px;
      margin-top: 20px;
    }

    .item {
      display: flex;
      font-size: 20px;
      font-weight: 400;
      color: #333333;
      align-items: center;

      a {
        font-size: 20px;
        font-weight: 400;
        color: #333333;

        &:hover {
          color: #bf0614;
        }
      }

      ul {
        display: flex;

        li {
          margin-right: 40px;

          &:nth-last-child(1) {
            margin-right: 40px;
          }

          cursor: pointer;

          &:hover {
            color: #bf0614;
          }
        }
      }

      .icon {
        display: flex;
        align-items: center;

        img {
          display: block;
          margin-right: 25px;
          cursor: pointer;

          &:nth-last-child(1) {
            margin-right: 0;
          }
        }

        .flex {
          position: relative;

          .img {
            &:hover + div {
              display: block;
            }
          }

          div {
            padding: 15px;
            background: #fff;
            position: absolute;
            left: -35px;
            display: none;
            top: 40px;
            border: 1px solid #ccc;

            img {
              width: 100px;
            }
          }
        }

        .flex1 {
          position: relative;

          .img {
            &:hover + div {
              display: block;
            }
          }

          div {
            padding: 15px;
            background: #fff;
            position: absolute;
            left: -30px;
            display: none;
            top: 40px;
            border: 1px solid #ccc;

            img {
              width: 100px;
            }
          }
        }
      }
    }

    .none {
      position: relative;
      display: none;
    }
  }
}

@media screen and (max-width: 1480px) {
  .header .nav {
    padding: 0 30px;
  }
}

@media screen and (max-width: 1120px) {
  .header .nav {
    padding: 0 30px;
  }
  .header .nav .item .icon {
    display: none;
  }
  .header .nav .item ul li {
    margin-right: 15px;

    &:nth-last-child(1) {
      margin-right: 0;
    }
  }
}

@media screen and (max-width: 750px) {
  .header .nav .item {
    display: none;
  }
  .header .nav .el-icon-more {
    display: block;
  }
  .header .nav .none {
    display: block;
  }
}
</style>
